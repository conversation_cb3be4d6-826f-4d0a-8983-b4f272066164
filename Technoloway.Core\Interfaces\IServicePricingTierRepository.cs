using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces;

public interface IServicePricingTierRepository : IRepository<ServicePricingTier>
{
    Task<IEnumerable<ServicePricingTier>> GetByServiceIdAsync(int serviceId);
    Task<IEnumerable<ServicePricingTier>> GetActiveByServiceIdAsync(int serviceId);
    Task<ServicePricingTier?> GetPopularTierAsync(int serviceId);
    Task<ServicePricingTier?> GetTierByQuantityAsync(int serviceId, int quantity);
    Task<IEnumerable<ServicePricingTier>> GetTiersInPriceRangeAsync(decimal minPrice, decimal maxPrice);
}
