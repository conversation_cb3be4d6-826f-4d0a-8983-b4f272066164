class QuoteBuilder {
    constructor() {
        this.currentStep = 1;
        this.services = [];
        this.selectedServices = new Map();
        this.totalPrice = 0;
        this.totalDeliveryDays = 0;
        
        this.initializeEventListeners();
        this.loadServices();
    }
    
    initializeEventListeners() {
        // Step navigation
        document.getElementById('next-to-configure').addEventListener('click', () => this.goToStep(2));
        document.getElementById('back-to-services').addEventListener('click', () => this.goToStep(1));
        document.getElementById('next-to-details').addEventListener('click', () => this.goToStep(3));
        document.getElementById('back-to-configure').addEventListener('click', () => this.goToStep(2));
        document.getElementById('next-to-review').addEventListener('click', () => this.goToStep(4));
        document.getElementById('back-to-details').addEventListener('click', () => this.goToStep(3));
        
        // Form submission
        document.getElementById('submit-quote').addEventListener('click', () => this.submitQuote());
        
        // Client form validation
        document.getElementById('client-form').addEventListener('input', () => this.validateClientForm());
    }
    
    async loadServices() {
        try {
            const response = await fetch('/api/PricingApi/services');
            if (response.ok) {
                this.services = await response.json();
                this.renderServiceList();
            } else {
                console.error('Failed to load services');
            }
        } catch (error) {
            console.error('Error loading services:', error);
        }
    }
    
    renderServiceList() {
        const serviceList = document.getElementById('service-list');
        serviceList.innerHTML = '';
        
        this.services.forEach(service => {
            const serviceCard = document.createElement('div');
            serviceCard.className = 'service-card card mb-3 p-3';
            serviceCard.dataset.serviceId = service.id;
            
            const isSelected = this.selectedServices.has(service.id);
            if (isSelected) {
                serviceCard.classList.add('selected');
            }
            
            serviceCard.innerHTML = `
                <div class="d-flex align-items-start">
                    <div class="form-check me-3">
                        <input class="form-check-input" type="checkbox" ${isSelected ? 'checked' : ''} 
                               data-service-id="${service.id}">
                    </div>
                    ${service.iconClass ? `<i class="${service.iconClass} fa-2x text-primary me-3 mt-1"></i>` : ''}
                    <div class="flex-grow-1">
                        <h5 class="mb-2">${service.name}</h5>
                        <p class="text-muted mb-2">${service.shortDescription}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="fw-bold text-primary">Starting from $${service.basePrice.toLocaleString()}</span>
                                <small class="text-muted ms-2">${service.estimatedDeliveryDays} days</small>
                            </div>
                            ${service.isFeatured ? '<span class="badge bg-warning">Featured</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
            
            // Add click event
            serviceCard.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const checkbox = serviceCard.querySelector('input[type="checkbox"]');
                    checkbox.click();
                }
            });
            
            // Add checkbox event
            const checkbox = serviceCard.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.addService(service);
                    serviceCard.classList.add('selected');
                } else {
                    this.removeService(service.id);
                    serviceCard.classList.remove('selected');
                }
                this.updateNextButton();
            });
            
            serviceList.appendChild(serviceCard);
        });
    }
    
    addService(service) {
        const serviceConfig = {
            service: service,
            tierId: null,
            quantity: 1,
            selectedFeatures: new Set(),
            calculatedPrice: service.basePrice,
            deliveryDays: service.estimatedDeliveryDays
        };
        
        this.selectedServices.set(service.id, serviceConfig);
        this.updateQuoteSummary();
    }
    
    removeService(serviceId) {
        this.selectedServices.delete(serviceId);
        this.updateQuoteSummary();
    }
    
    addServiceFromConfig(config) {
        const service = this.services.find(s => s.id === config.serviceId);
        if (service) {
            const serviceConfig = {
                service: service,
                tierId: config.tierId,
                quantity: config.quantity,
                selectedFeatures: new Set(config.selectedFeatures),
                calculatedPrice: 0,
                deliveryDays: service.estimatedDeliveryDays
            };
            
            this.selectedServices.set(service.id, serviceConfig);
            this.calculateServicePrice(serviceConfig);
            this.updateQuoteSummary();
            this.renderServiceList(); // Re-render to show selected state
        }
    }
    
    async calculateServicePrice(serviceConfig) {
        try {
            const request = {
                serviceId: serviceConfig.service.id,
                tierId: serviceConfig.tierId,
                quantity: serviceConfig.quantity,
                selectedFeatureIds: Array.from(serviceConfig.selectedFeatures),
                featureQuantities: {}
            };
            
            // Set feature quantities (default to 1)
            serviceConfig.selectedFeatures.forEach(featureId => {
                request.featureQuantities[featureId] = 1;
            });
            
            const response = await fetch('/Pricing/CalculatePrice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request)
            });
            
            if (response.ok) {
                const result = await response.json();
                serviceConfig.calculatedPrice = result.totalPrice;
                serviceConfig.deliveryDays = result.estimatedDeliveryDays;
                this.updateQuoteSummary();
            }
        } catch (error) {
            console.error('Error calculating service price:', error);
        }
    }
    
    updateNextButton() {
        const nextButton = document.getElementById('next-to-configure');
        nextButton.disabled = this.selectedServices.size === 0;
    }
    
    goToStep(step) {
        // Hide current step
        document.querySelectorAll('.quote-step').forEach(s => s.classList.remove('active'));
        document.querySelectorAll('.step').forEach(s => s.classList.remove('active', 'completed'));
        
        // Show new step
        document.getElementById(`step-${step}`).classList.add('active');
        
        // Update step indicator
        for (let i = 1; i <= 4; i++) {
            const stepElement = document.querySelector(`[data-step="${i}"]`);
            if (i < step) {
                stepElement.classList.add('completed');
            } else if (i === step) {
                stepElement.classList.add('active');
            }
        }
        
        this.currentStep = step;
        
        // Handle step-specific logic
        if (step === 2) {
            this.renderServiceConfiguration();
        } else if (step === 4) {
            this.renderReviewSummary();
        }
    }
    
    renderServiceConfiguration() {
        const container = document.getElementById('selected-services');
        container.innerHTML = '';
        
        this.selectedServices.forEach((config, serviceId) => {
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'quote-item';
            serviceDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5>${config.service.name}</h5>
                        <p class="text-muted mb-0">${config.service.description}</p>
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="quoteBuilder.removeService(${serviceId})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control" value="${config.quantity}" min="1" max="10"
                               onchange="quoteBuilder.updateServiceQuantity(${serviceId}, this.value)">
                    </div>
                    <div class="col-md-9">
                        <label class="form-label">Pricing Tier</label>
                        <select class="form-select" onchange="quoteBuilder.updateServiceTier(${serviceId}, this.value)">
                            <option value="">Base Service - $${config.service.basePrice.toLocaleString()}</option>
                            ${config.service.pricingTiers.map(tier => `
                                <option value="${tier.id}" ${config.tierId === tier.id ? 'selected' : ''}>
                                    ${tier.name} - $${tier.price.toLocaleString()}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                </div>
                
                ${config.service.features.length > 0 ? `
                    <div class="mt-3">
                        <label class="form-label">Additional Features</label>
                        <div class="row g-2">
                            ${config.service.features.filter(f => !f.isRequired).map(feature => `
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               ${config.selectedFeatures.has(feature.id) ? 'checked' : ''}
                                               onchange="quoteBuilder.toggleServiceFeature(${serviceId}, ${feature.id}, this.checked)">
                                        <label class="form-check-label">
                                            ${feature.name} (+$${feature.additionalPrice.toLocaleString()})
                                        </label>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <div class="mt-3 p-3 bg-light rounded">
                    <div class="d-flex justify-content-between">
                        <span>Service Total:</span>
                        <span class="fw-bold">$${config.calculatedPrice.toLocaleString()}</span>
                    </div>
                </div>
            `;
            
            container.appendChild(serviceDiv);
        });
    }
    
    updateServiceQuantity(serviceId, quantity) {
        const config = this.selectedServices.get(serviceId);
        if (config) {
            config.quantity = parseInt(quantity) || 1;
            this.calculateServicePrice(config);
        }
    }
    
    updateServiceTier(serviceId, tierId) {
        const config = this.selectedServices.get(serviceId);
        if (config) {
            config.tierId = tierId ? parseInt(tierId) : null;
            this.calculateServicePrice(config);
        }
    }
    
    toggleServiceFeature(serviceId, featureId, checked) {
        const config = this.selectedServices.get(serviceId);
        if (config) {
            if (checked) {
                config.selectedFeatures.add(featureId);
            } else {
                config.selectedFeatures.delete(featureId);
            }
            this.calculateServicePrice(config);
        }
    }
    
    validateClientForm() {
        const name = document.getElementById('clientName').value.trim();
        const email = document.getElementById('clientEmail').value.trim();
        const nextButton = document.getElementById('next-to-review');
        
        nextButton.disabled = !name || !email || !this.isValidEmail(email);
    }
    
    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    
    renderReviewSummary() {
        // Client summary
        const clientSummary = document.getElementById('client-summary');
        const name = document.getElementById('clientName').value;
        const email = document.getElementById('clientEmail').value;
        const phone = document.getElementById('clientPhone').value;
        const company = document.getElementById('companyName').value;
        
        clientSummary.innerHTML = `
            <div><strong>Name:</strong> ${name}</div>
            <div><strong>Email:</strong> ${email}</div>
            ${phone ? `<div><strong>Phone:</strong> ${phone}</div>` : ''}
            ${company ? `<div><strong>Company:</strong> ${company}</div>` : ''}
        `;
        
        // Services summary
        const servicesSummary = document.getElementById('services-summary');
        servicesSummary.innerHTML = '';
        
        this.selectedServices.forEach(config => {
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'quote-item';
            serviceDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6>${config.service.name} (×${config.quantity})</h6>
                        ${config.tierId ? `<small class="text-muted">Tier: ${config.service.pricingTiers.find(t => t.id === config.tierId)?.name}</small><br>` : ''}
                        ${config.selectedFeatures.size > 0 ? `<small class="text-muted">Features: ${Array.from(config.selectedFeatures).length} selected</small>` : ''}
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">$${config.calculatedPrice.toLocaleString()}</div>
                        <small class="text-muted">${config.deliveryDays} days</small>
                    </div>
                </div>
            `;
            servicesSummary.appendChild(serviceDiv);
        });
        
        // Update final totals
        this.updateQuoteSummary();
        document.getElementById('final-total-price').textContent = `$${this.totalPrice.toLocaleString()}`;
        document.getElementById('final-delivery-days').textContent = this.totalDeliveryDays;
    }
    
    updateQuoteSummary() {
        this.totalPrice = 0;
        this.totalDeliveryDays = 0;
        
        const quoteItems = document.getElementById('quote-items');
        
        if (this.selectedServices.size === 0) {
            quoteItems.innerHTML = '<p class="text-muted text-center py-4">No services selected yet</p>';
        } else {
            quoteItems.innerHTML = '';
            
            this.selectedServices.forEach(config => {
                this.totalPrice += config.calculatedPrice;
                this.totalDeliveryDays = Math.max(this.totalDeliveryDays, config.deliveryDays);
                
                const itemDiv = document.createElement('div');
                itemDiv.className = 'd-flex justify-content-between align-items-center mb-2';
                itemDiv.innerHTML = `
                    <div>
                        <div class="fw-medium">${config.service.name}</div>
                        <small class="text-muted">Qty: ${config.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div>$${config.calculatedPrice.toLocaleString()}</div>
                    </div>
                `;
                quoteItems.appendChild(itemDiv);
            });
        }
        
        // Update all total displays
        document.getElementById('sidebar-total').textContent = `$${this.totalPrice.toLocaleString()}`;
        document.getElementById('sidebar-delivery').textContent = this.totalDeliveryDays;
        document.getElementById('mobile-total').textContent = `$${this.totalPrice.toLocaleString()}`;
        document.getElementById('mobile-delivery').textContent = this.totalDeliveryDays;
    }
    
    async submitQuote() {
        const submitButton = document.getElementById('submit-quote');
        const originalText = submitButton.innerHTML;
        
        try {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
            
            const quoteRequest = {
                clientName: document.getElementById('clientName').value,
                clientEmail: document.getElementById('clientEmail').value,
                clientPhone: document.getElementById('clientPhone').value,
                companyName: document.getElementById('companyName').value,
                projectDescription: document.getElementById('projectDescription').value,
                items: []
            };
            
            this.selectedServices.forEach(config => {
                const item = {
                    serviceId: config.service.id,
                    servicePricingTierId: config.tierId,
                    quantity: config.quantity,
                    configuration: JSON.stringify({
                        selectedTier: config.tierId,
                        selectedFeatures: Array.from(config.selectedFeatures)
                    }),
                    selectedFeatures: Array.from(config.selectedFeatures).map(featureId => ({
                        serviceFeatureId: featureId,
                        quantity: 1,
                        configuration: ''
                    }))
                };
                quoteRequest.items.push(item);
            });
            
            const response = await fetch('/Pricing/SubmitQuote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(quoteRequest)
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Redirect to quote page
                    window.location.href = `/Pricing/Quote/${result.quoteId}`;
                } else {
                    alert('Error submitting quote: ' + result.message);
                }
            } else {
                alert('Error submitting quote. Please try again.');
            }
        } catch (error) {
            console.error('Error submitting quote:', error);
            alert('Error submitting quote. Please try again.');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }
}

// Make QuoteBuilder available globally
window.QuoteBuilder = QuoteBuilder;
