using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces;

public interface IQuoteRepository : IRepository<Quote>
{
    Task<Quote?> GetByQuoteNumberAsync(string quoteNumber);
    Task<Quote?> GetWithItemsAsync(int id);
    Task<Quote?> GetWithItemsAndFeaturesAsync(int id);
    Task<IEnumerable<Quote>> GetByClientEmailAsync(string clientEmail);
    Task<IEnumerable<Quote>> GetByStatusAsync(string status);
    Task<IEnumerable<Quote>> GetExpiredQuotesAsync();
    Task<string> GenerateQuoteNumberAsync();
    Task<decimal> CalculateQuoteTotalAsync(int quoteId);
    Task<bool> UpdateQuoteStatusAsync(int quoteId, string status);
    Task<IEnumerable<Quote>> GetQuotesByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<string, int>> GetQuoteStatusCountsAsync();
    Task<decimal> GetTotalQuoteValueByStatusAsync(string status);
}
