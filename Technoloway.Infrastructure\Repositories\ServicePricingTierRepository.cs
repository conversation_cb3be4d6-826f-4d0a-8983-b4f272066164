using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories;

public class ServicePricingTierRepository : Repository<ServicePricingTier>, IServicePricingTierRepository
{
    public ServicePricingTierRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<ServicePricingTier>> GetByServiceIdAsync(int serviceId)
    {
        var tiers = await _context.ServicePricingTiers
            .Where(spt => spt.ServiceId == serviceId && !spt.IsDeleted)
            .OrderBy(spt => spt.DisplayOrder)
            .ToListAsync();

        // Order by price on client side since SQLite doesn't support decimal ordering
        return tiers.OrderBy(spt => spt.DisplayOrder).ThenBy(spt => spt.Price);
    }

    public async Task<IEnumerable<ServicePricingTier>> GetActiveByServiceIdAsync(int serviceId)
    {
        var tiers = await _context.ServicePricingTiers
            .Where(spt => spt.ServiceId == serviceId && spt.IsActive && !spt.IsDeleted)
            .OrderBy(spt => spt.DisplayOrder)
            .ToListAsync();

        // Order by price on client side since SQLite doesn't support decimal ordering
        return tiers.OrderBy(spt => spt.DisplayOrder).ThenBy(spt => spt.Price);
    }

    public async Task<ServicePricingTier?> GetPopularTierAsync(int serviceId)
    {
        return await _context.ServicePricingTiers
            .Where(spt => spt.ServiceId == serviceId && spt.IsPopular && spt.IsActive && !spt.IsDeleted)
            .FirstOrDefaultAsync();
    }

    public async Task<ServicePricingTier?> GetTierByQuantityAsync(int serviceId, int quantity)
    {
        var tiers = await _context.ServicePricingTiers
            .Where(spt => spt.ServiceId == serviceId &&
                         spt.MinQuantity <= quantity &&
                         (spt.MaxQuantity == null || spt.MaxQuantity >= quantity) &&
                         spt.IsActive && !spt.IsDeleted)
            .ToListAsync();

        // Order by price on client side since SQLite doesn't support decimal ordering
        return tiers.OrderBy(spt => spt.Price).FirstOrDefault();
    }

    public async Task<IEnumerable<ServicePricingTier>> GetTiersInPriceRangeAsync(decimal minPrice, decimal maxPrice)
    {
        var tiers = await _context.ServicePricingTiers
            .Where(spt => spt.Price >= minPrice && spt.Price <= maxPrice && spt.IsActive && !spt.IsDeleted)
            .ToListAsync();

        // Order by price on client side since SQLite doesn't support decimal ordering
        return tiers.OrderBy(spt => spt.Price);
    }
}
