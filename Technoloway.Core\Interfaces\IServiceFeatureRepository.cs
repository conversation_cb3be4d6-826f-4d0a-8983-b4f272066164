using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces;

public interface IServiceFeatureRepository : IRepository<ServiceFeature>
{
    Task<IEnumerable<ServiceFeature>> GetByServiceIdAsync(int serviceId);
    Task<IEnumerable<ServiceFeature>> GetActiveByServiceIdAsync(int serviceId);
    Task<IEnumerable<ServiceFeature>> GetByFeatureTypeAsync(string featureType);
    Task<IEnumerable<ServiceFeature>> GetByCategoryAsync(string category);
    Task<IEnumerable<ServiceFeature>> GetRequiredFeaturesAsync(int serviceId);
    Task<IEnumerable<ServiceFeature>> GetOptionalFeaturesAsync(int serviceId);
    Task<decimal> CalculateFeaturesTotalAsync(IEnumerable<int> featureIds, Dictionary<int, int> quantities);
}
