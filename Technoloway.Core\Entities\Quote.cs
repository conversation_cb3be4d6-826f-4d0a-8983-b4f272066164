using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Quote : BaseEntity
{
    [Required(ErrorMessage = "Quote number is required")]
    [StringLength(50, ErrorMessage = "Quote number cannot exceed 50 characters")]
    public string QuoteNumber { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Client name is required")]
    [StringLength(100, ErrorMessage = "Client name cannot exceed 100 characters")]
    public string ClientName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Client email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Client email cannot exceed 100 characters")]
    public string ClientEmail { get; set; } = string.Empty;
    
    [StringLength(20, ErrorMessage = "Client phone cannot exceed 20 characters")]
    public string ClientPhone { get; set; } = string.Empty;
    
    [StringLength(100, ErrorMessage = "Company name cannot exceed 100 characters")]
    public string CompanyName { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Project description cannot exceed 1000 characters")]
    public string ProjectDescription { get; set; } = string.Empty;
    
    [Range(0, double.MaxValue, ErrorMessage = "Subtotal must be greater than or equal to 0")]
    public decimal Subtotal { get; set; }
    
    [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
    public decimal TaxRate { get; set; } = 0;
    
    [Range(0, double.MaxValue, ErrorMessage = "Tax amount must be greater than or equal to 0")]
    public decimal TaxAmount { get; set; }
    
    [Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
    public decimal DiscountPercentage { get; set; } = 0;
    
    [Range(0, double.MaxValue, ErrorMessage = "Discount amount must be greater than or equal to 0")]
    public decimal DiscountAmount { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Total amount must be greater than or equal to 0")]
    public decimal TotalAmount { get; set; }
    
    [Required(ErrorMessage = "Status is required")]
    [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
    public string Status { get; set; } = "Draft"; // Draft, Sent, Accepted, Rejected, Expired
    
    [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
    public string Notes { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Admin notes cannot exceed 1000 characters")]
    public string AdminNotes { get; set; } = string.Empty;
    
    public DateTime? SentAt { get; set; }
    public DateTime? AcceptedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    
    [Range(1, 365, ErrorMessage = "Estimated delivery days must be between 1 and 365")]
    public int EstimatedDeliveryDays { get; set; } = 30;
    
    [StringLength(3, ErrorMessage = "Currency cannot exceed 3 characters")]
    public string Currency { get; set; } = "USD";
    
    // Optional client relationship
    public int? ClientId { get; set; }
    public Client? Client { get; set; }
    
    // Navigation properties
    public ICollection<QuoteItem> Items { get; set; } = new List<QuoteItem>();
}
