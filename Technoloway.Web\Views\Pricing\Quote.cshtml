@model Technoloway.Web.Models.QuoteResponseDto
@{
    ViewData["Title"] = $"Quote {Model.QuoteNumber}";
    ViewData["Description"] = $"Quote details for {Model.ClientName}";
}

@section Styles {
    <style>
        .quote-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .quote-card {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 15px;
        }
        .quote-item {
            border-bottom: 1px solid #e9ecef;
            padding: 1.5rem 0;
        }
        .quote-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }
        .quote-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.25rem 0;
            color: #6c757d;
        }
        .feature-list li i {
            color: var(--bs-success);
            margin-right: 0.5rem;
        }
        @@media print {
            .no-print {
                display: none !important;
            }
            .quote-header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
}

<!-- Quote Header -->
<section class="quote-header no-print">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-6 fw-bold mb-2">Quote @Model.QuoteNumber</h1>
                <p class="lead mb-0">Prepared for @Model.ClientName</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <span class="status-badge badge bg-@(Model.Status switch {
                    "Draft" => "secondary",
                    "Sent" => "primary",
                    "Accepted" => "success",
                    "Rejected" => "danger",
                    "Expired" => "warning",
                    _ => "secondary"
                })">
                    @Model.Status
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Quote Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Quote Details -->
            <div class="col-lg-8">
                <div class="quote-card card mb-4">
                    <div class="card-body p-4">
                        <!-- Client Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Client Information</h5>
                                <div class="mb-2"><strong>Name:</strong> @Model.ClientName</div>
                                <div class="mb-2"><strong>Email:</strong> @Model.ClientEmail</div>
                                <div class="mb-2"><strong>Quote Date:</strong> @Model.CreatedAt.ToString("MMMM dd, yyyy")</div>
                                @if (Model.ExpiresAt.HasValue)
                                {
                                    <div class="mb-2">
                                        <strong>Expires:</strong> @Model.ExpiresAt.Value.ToString("MMMM dd, yyyy")
                                        @if (Model.ExpiresAt < DateTime.UtcNow && Model.Status == "Sent")
                                        {
                                            <span class="badge bg-warning ms-2">Expired</span>
                                        }
                                    </div>
                                }
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Project Details</h5>
                                <div class="mb-2"><strong>Quote Number:</strong> @Model.QuoteNumber</div>
                                <div class="mb-2"><strong>Estimated Delivery:</strong> @Model.EstimatedDeliveryDays days</div>
                                <div class="mb-2"><strong>Total Value:</strong> $@Model.TotalAmount.ToString("N2")</div>
                            </div>
                        </div>

                        <!-- Services -->
                        <h5 class="text-primary mb-3">Services & Features</h5>
                        @foreach (var item in Model.Items)
                        {
                            <div class="quote-item">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-start">
                                            @if (!string.IsNullOrEmpty(item.Service.IconClass))
                                            {
                                                <i class="@item.Service.IconClass fa-2x text-primary me-3 mt-1"></i>
                                            }
                                            <div>
                                                <h6 class="mb-2">@item.Name</h6>
                                                <p class="text-muted mb-2">@item.Description</p>
                                                
                                                @if (item.ServicePricingTier != null)
                                                {
                                                    <div class="mb-2">
                                                        <span class="badge bg-info">@item.ServicePricingTier.Name</span>
                                                    </div>
                                                }
                                                
                                                @if (item.SelectedFeatures.Any())
                                                {
                                                    <div>
                                                        <small class="text-muted fw-medium">Additional Features:</small>
                                                        <ul class="feature-list mt-1">
                                                            @foreach (var feature in item.SelectedFeatures)
                                                            {
                                                                <li>
                                                                    <i class="fas fa-check"></i>
                                                                    @feature.ServiceFeature.Name
                                                                    @if (feature.TotalPrice > 0)
                                                                    {
                                                                        <span class="text-success">(+$@feature.TotalPrice.ToString("N0"))</span>
                                                                    }
                                                                </li>
                                                            }
                                                        </ul>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <div class="mb-2">
                                            <span class="text-muted">Quantity:</span> @item.Quantity
                                        </div>
                                        <div class="mb-2">
                                            <span class="text-muted">Unit Price:</span> $@item.UnitPrice.ToString("N2")
                                        </div>
                                        <div class="h5 text-primary fw-bold">
                                            $@item.TotalPrice.ToString("N2")
                                        </div>
                                        <small class="text-muted">@item.DeliveryDays days delivery</small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Quote Summary -->
            <div class="col-lg-4">
                <div class="quote-summary">
                    <h4 class="mb-3">Quote Summary</h4>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>$@Model.Subtotal.ToString("N2")</span>
                    </div>
                    
                    @if (Model.DiscountAmount > 0)
                    {
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-$@Model.DiscountAmount.ToString("N2")</span>
                        </div>
                    }
                    
                    @if (Model.TaxAmount > 0)
                    {
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>$@Model.TaxAmount.ToString("N2")</span>
                        </div>
                    }
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="h5 fw-bold">Total:</span>
                        <span class="h4 text-primary fw-bold">$@Model.TotalAmount.ToString("N2")</span>
                    </div>
                    
                    <div class="text-center mb-3">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            Estimated delivery: @Model.EstimatedDeliveryDays days
                        </small>
                    </div>
                    
                    @if (Model.Status == "Sent" && Model.ExpiresAt > DateTime.UtcNow)
                    {
                        <div class="d-grid gap-2 no-print">
                            <button type="button" class="btn btn-success btn-lg" onclick="acceptQuote()">
                                <i class="fas fa-check me-2"></i>Accept Quote
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="rejectQuote()">
                                <i class="fas fa-times me-2"></i>Decline Quote
                            </button>
                        </div>
                    }
                    
                    <div class="d-grid gap-2 mt-3 no-print">
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Print Quote
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="downloadPDF()">
                            <i class="fas fa-download me-2"></i>Download PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5 bg-light no-print">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">Questions About This Quote?</h3>
                <p class="mb-0">Our team is here to help you understand every detail and customize the solution to your needs.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="@Url.Action("Index", "Contact")" class="btn btn-primary btn-lg">
                    <i class="fas fa-envelope me-2"></i>Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function acceptQuote() {
            if (confirm('Are you sure you want to accept this quote?')) {
                updateQuoteStatus('Accepted');
            }
        }
        
        function rejectQuote() {
            if (confirm('Are you sure you want to decline this quote?')) {
                updateQuoteStatus('Rejected');
            }
        }
        
        async function updateQuoteStatus(status) {
            try {
                const response = await fetch(`/api/PricingApi/quotes/@Model.Id/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: status })
                });
                
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error updating quote status. Please try again.');
                }
            } catch (error) {
                console.error('Error updating quote status:', error);
                alert('Error updating quote status. Please try again.');
            }
        }
        
        function downloadPDF() {
            // This would integrate with a PDF generation service
            alert('PDF download functionality would be implemented here.');
        }
        
        // Auto-refresh if quote is about to expire
        @if (Model.Status == "Sent" && Model.ExpiresAt.HasValue)
        {
            <text>
            var expiresAt = new Date('@Model.ExpiresAt.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")');
            var now = new Date();
            var timeUntilExpiry = expiresAt.getTime() - now.getTime();

            if (timeUntilExpiry > 0 && timeUntilExpiry < 24 * 60 * 60 * 1000) { // Less than 24 hours
                setTimeout(() => {
                    location.reload();
                }, timeUntilExpiry);
            }
            </text>
        }
    </script>
}
