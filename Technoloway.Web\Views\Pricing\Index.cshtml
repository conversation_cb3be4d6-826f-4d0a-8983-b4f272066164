@model Technoloway.Web.Models.PricingIndexViewModel
@{
    ViewData["Title"] = "Pricing - Choose Your Perfect Plan";
    ViewData["Description"] = "Transparent pricing for all our software development services. Choose from our flexible plans or get a custom quote.";
}

@section Styles {
    <style>
        .pricing-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .pricing-card:hover {
            transform: translateY(-5px);
            border-color: var(--bs-primary);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .pricing-card.featured {
            border-color: var(--bs-primary);
            position: relative;
        }
        .pricing-card.featured::before {
            content: "Most Popular";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bs-primary);
            color: white;
            padding: 5px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .price-display {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--bs-primary);
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: var(--bs-success);
            margin-right: 0.5rem;
        }
        .category-filter {
            margin-bottom: 2rem;
        }
        .category-btn {
            margin: 0.25rem;
            border-radius: 25px;
        }
        .hero-pricing {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
    </style>
}

<!-- Test Message -->
@if (ViewBag.TestMessage != null)
{
    <div class="alert alert-success text-center">
        <h4>@ViewBag.TestMessage</h4>
        <p>The pricing page is working! Navigation access has been successfully implemented.</p>
    </div>
}

<!-- Hero Section -->
@if (ViewBag.HeroSection != null)
{
    <section class="hero-pricing">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">@ViewBag.HeroSection.MainTitle</h1>
                    <p class="lead mb-4">@ViewBag.HeroSection.MainDescription</p>
                    <div class="d-flex gap-3">
                        <a href="#pricing-plans" class="btn btn-light btn-lg">View Plans</a>
                        <a href="#custom-quote" class="btn btn-outline-light btn-lg">Get Custom Quote</a>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="text-center">
                        <i class="fas fa-calculator fa-5x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>
}
else
{
    <section class="hero-pricing">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Pricing & Services</h1>
                    <p class="lead mb-4">Transparent pricing for all our software development services. Choose from our flexible plans or get a custom quote.</p>
                    <div class="d-flex gap-3">
                        <a href="#pricing-plans" class="btn btn-light btn-lg">View Plans</a>
                        <a href="#custom-quote" class="btn btn-outline-light btn-lg">Get Custom Quote</a>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="text-center">
                        <i class="fas fa-calculator fa-5x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>
}

<!-- Category Filter -->
@if (Model.Categories.Any())
{
    <section class="category-filter py-4 bg-light">
        <div class="container">
            <div class="text-center">
                <h5 class="mb-3">Filter by Category</h5>
                <div class="d-flex flex-wrap justify-content-center">
                    <a href="@Url.Action("Index")" class="btn @(string.IsNullOrEmpty(Model.SelectedCategory) ? "btn-primary" : "btn-outline-primary") category-btn">All Services</a>
                    @foreach (var category in Model.Categories)
                    {
                        <a href="@Url.Action("Index", new { category })" class="btn @(Model.SelectedCategory == category ? "btn-primary" : "btn-outline-primary") category-btn">@category</a>
                    }
                </div>
            </div>
        </div>
    </section>
}

<!-- Featured Services -->
@if (Model.FeaturedServices.Any())
{
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Featured Services</h2>
                <p class="text-muted">Our most popular development services</p>
            </div>
            <div class="row g-4">
                @foreach (var service in Model.FeaturedServices.Take(3))
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="card pricing-card featured h-100">
                            <div class="card-body text-center p-4">
                                @if (!string.IsNullOrEmpty(service.IconClass))
                                {
                                    <i class="@service.IconClass fa-3x text-primary mb-3"></i>
                                }
                                <h4 class="card-title">@service.Name</h4>
                                <p class="text-muted mb-3">@service.ShortDescription</p>
                                <div class="price-display mb-3">
                                    @if (service.PricingModel == "Fixed")
                                    {
                                        <span>$@service.BasePrice.ToString("N0")</span>
                                        <small class="text-muted d-block">Starting from</small>
                                    }
                                    else if (service.PricingTiers.Any())
                                    {
                                        var minPrice = service.PricingTiers.Min(t => t.Price);
                                        <span>$@minPrice.ToString("N0")</span>
                                        <small class="text-muted d-block">Starting from</small>
                                    }
                                    else
                                    {
                                        <span>Custom</span>
                                        <small class="text-muted d-block">Quote</small>
                                    }
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="@Url.Action("Configure", new { id = service.Id })" class="btn btn-primary">Configure & Price</a>
                                    <a href="@Url.Action("Details", "Services", new { id = service.Id })" class="btn btn-outline-secondary">Learn More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </section>
}

<!-- All Services -->
<section id="pricing-plans" class="py-5 @(Model.FeaturedServices.Any() ? "bg-light" : "")">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">All Services</h2>
            <p class="text-muted">Complete range of software development services</p>
        </div>
        @if (Model.Services.Any())
        {
            <div class="row g-4">
                @foreach (var service in Model.Services)
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="card pricing-card h-100">
                            <div class="card-body p-4">
                                <div class="text-center mb-3">
                                    @if (!string.IsNullOrEmpty(service.IconClass))
                                    {
                                        <i class="@service.IconClass fa-2x text-primary mb-2"></i>
                                    }
                                    <h5 class="card-title">@service.Name</h5>
                                    <p class="text-muted small">@service.ShortDescription</p>
                                </div>
                            
                            <div class="text-center mb-3">
                                @if (service.PricingModel == "Fixed")
                                {
                                    <div class="price-display">$@service.BasePrice.ToString("N0")</div>
                                    <small class="text-muted">Fixed Price</small>
                                }
                                else if (service.PricingTiers.Any())
                                {
                                    var minPrice = service.PricingTiers.Min(t => t.Price);
                                    var maxPrice = service.PricingTiers.Max(t => t.Price);
                                    <div class="price-display">$@minPrice.ToString("N0") - $@maxPrice.ToString("N0")</div>
                                    <small class="text-muted">Multiple Tiers</small>
                                }
                                else
                                {
                                    <div class="price-display">Custom</div>
                                    <small class="text-muted">Contact for Quote</small>
                                }
                            </div>

                            @if (service.Features.Any())
                            {
                                <ul class="feature-list mb-4">
                                    @foreach (var feature in service.Features.Where(f => f.FeatureType == "Included").Take(4))
                                    {
                                        <li><i class="fas fa-check"></i> @feature.Name</li>
                                    }
                                    @if (service.Features.Count(f => f.FeatureType == "Included") > 4)
                                    {
                                        <li class="text-muted"><i class="fas fa-plus"></i> @(service.Features.Count(f => f.FeatureType == "Included") - 4) more features</li>
                                    }
                                </ul>
                            }

                            <div class="d-grid gap-2 mt-auto">
                                <a href="@Url.Action("Configure", new { id = service.Id })" class="btn btn-primary">Configure & Price</a>
                                <a href="@Url.Action("Details", "Services", new { id = service.Id })" class="btn btn-outline-secondary btn-sm">View Details</a>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent text-center">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> @service.EstimatedDeliveryDays days delivery
                            </small>
                        </div>
                    </div>
                </div>
            }
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-tools fa-4x text-muted mb-3"></i>
                <h4>Services Coming Soon</h4>
                <p class="text-muted">We're setting up our pricing structure. Please check back soon or contact us for a custom quote.</p>
                <a href="@Url.Action("QuoteBuilder")" class="btn btn-primary">Get Custom Quote</a>
            </div>
        }
    </div>
</section>

<!-- Custom Quote Section -->
<section id="custom-quote" class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">Need a Custom Solution?</h3>
                <p class="mb-0">Our services don't fit your exact needs? Let's build a custom solution together. Get a personalized quote based on your specific requirements.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="@Url.Action("QuoteBuilder")" class="btn btn-light btn-lg">
                    <i class="fas fa-calculator me-2"></i>Get Custom Quote
                </a>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe pricing cards
        document.querySelectorAll('.pricing-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
}
