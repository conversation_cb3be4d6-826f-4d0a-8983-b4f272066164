using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;
using System.Text.Json;

namespace Technoloway.Web.Services;

public interface IPricingService
{
    Task<PriceCalculationResponse> CalculatePriceAsync(PriceCalculationRequest request);
    Task<QuoteResponseDto> CreateQuoteAsync(QuoteRequestDto request);
    Task<QuoteResponseDto?> GetQuoteAsync(int id);
    Task<QuoteResponseDto?> GetQuoteByNumberAsync(string quoteNumber);
    Task<bool> UpdateQuoteStatusAsync(int id, string status);
    Task<IEnumerable<ServiceDto>> GetServicesWithPricingAsync();
    Task<ServiceDto?> GetServiceWithPricingAsync(int id);
    Task<decimal> CalculateQuoteTotalAsync(Quote quote);
}

public class PricingService : IPricingService
{
    private readonly IRepository<Service> _serviceRepository;
    private readonly IServiceFeatureRepository _serviceFeatureRepository;
    private readonly IServicePricingTierRepository _servicePricingTierRepository;
    private readonly IQuoteRepository _quoteRepository;
    private readonly IRepository<QuoteItem> _quoteItemRepository;
    private readonly IRepository<QuoteItemFeature> _quoteItemFeatureRepository;

    public PricingService(
        IRepository<Service> serviceRepository,
        IServiceFeatureRepository serviceFeatureRepository,
        IServicePricingTierRepository servicePricingTierRepository,
        IQuoteRepository quoteRepository,
        IRepository<QuoteItem> quoteItemRepository,
        IRepository<QuoteItemFeature> quoteItemFeatureRepository)
    {
        _serviceRepository = serviceRepository;
        _serviceFeatureRepository = serviceFeatureRepository;
        _servicePricingTierRepository = servicePricingTierRepository;
        _quoteRepository = quoteRepository;
        _quoteItemRepository = quoteItemRepository;
        _quoteItemFeatureRepository = quoteItemFeatureRepository;
    }

    public async Task<PriceCalculationResponse> CalculatePriceAsync(PriceCalculationRequest request)
    {
        var service = await _serviceRepository.GetByIdAsync(request.ServiceId);
        if (service == null)
            throw new ArgumentException("Service not found");

        var response = new PriceCalculationResponse();
        var breakdown = new List<PriceBreakdownItem>();

        // Calculate base price
        decimal basePrice = service.BasePrice;
        
        // If tier is selected, use tier pricing
        if (request.TierId.HasValue)
        {
            var tier = await _servicePricingTierRepository.GetByIdAsync(request.TierId.Value);
            if (tier != null && tier.ServiceId == request.ServiceId)
            {
                basePrice = tier.Price;
                response.EstimatedDeliveryDays = tier.DeliveryDays;
                
                breakdown.Add(new PriceBreakdownItem
                {
                    Name = $"{service.Name} - {tier.Name}",
                    Quantity = request.Quantity,
                    UnitPrice = tier.Price,
                    TotalPrice = tier.Price * request.Quantity,
                    Type = "Tier"
                });
            }
        }
        else
        {
            response.EstimatedDeliveryDays = service.EstimatedDeliveryDays;
            breakdown.Add(new PriceBreakdownItem
            {
                Name = service.Name,
                Quantity = request.Quantity,
                UnitPrice = basePrice,
                TotalPrice = basePrice * request.Quantity,
                Type = "Service"
            });
        }

        response.BasePrice = basePrice * request.Quantity;

        // Calculate features price
        decimal featuresPrice = 0;
        if (request.SelectedFeatureIds.Any())
        {
            var features = await _serviceFeatureRepository.GetByIdsAsync(request.SelectedFeatureIds);
            foreach (var feature in features.Where(f => f.ServiceId == request.ServiceId))
            {
                var quantity = request.FeatureQuantities.ContainsKey(feature.Id) 
                    ? request.FeatureQuantities[feature.Id] 
                    : 1;
                
                var featureTotal = feature.AdditionalPrice * quantity;
                featuresPrice += featureTotal;

                breakdown.Add(new PriceBreakdownItem
                {
                    Name = feature.Name,
                    Quantity = quantity,
                    UnitPrice = feature.AdditionalPrice,
                    TotalPrice = featureTotal,
                    Type = "Feature"
                });

                // Add to delivery time if feature affects it
                if (feature.PricingType == "Monthly")
                {
                    response.EstimatedDeliveryDays += 7; // Add a week for monthly features
                }
            }
        }

        response.FeaturesPrice = featuresPrice;
        response.TotalPrice = response.BasePrice + response.FeaturesPrice;
        response.Breakdown = breakdown;

        return response;
    }

    public async Task<QuoteResponseDto> CreateQuoteAsync(QuoteRequestDto request)
    {
        // Generate quote number
        var quoteNumber = await _quoteRepository.GenerateQuoteNumberAsync();

        // Create quote entity
        var quote = new Quote
        {
            QuoteNumber = quoteNumber,
            ClientName = request.ClientName,
            ClientEmail = request.ClientEmail,
            ClientPhone = request.ClientPhone,
            CompanyName = request.CompanyName,
            ProjectDescription = request.ProjectDescription,
            Status = "Draft",
            Currency = "USD",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Calculate totals
        decimal subtotal = 0;
        int maxDeliveryDays = 0;

        // Create quote items
        var quoteItems = new List<QuoteItem>();
        foreach (var itemRequest in request.Items)
        {
            var service = await _serviceRepository.GetByIdAsync(itemRequest.ServiceId);
            if (service == null) continue;

            var priceCalc = await CalculatePriceAsync(new PriceCalculationRequest
            {
                ServiceId = itemRequest.ServiceId,
                TierId = itemRequest.ServicePricingTierId,
                Quantity = itemRequest.Quantity,
                SelectedFeatureIds = itemRequest.SelectedFeatures.Select(f => f.ServiceFeatureId).ToList(),
                FeatureQuantities = itemRequest.SelectedFeatures.ToDictionary(f => f.ServiceFeatureId, f => f.Quantity)
            });

            var quoteItem = new QuoteItem
            {
                Name = service.Name,
                Description = service.Description,
                Quantity = itemRequest.Quantity,
                UnitPrice = priceCalc.BasePrice / itemRequest.Quantity,
                TotalPrice = priceCalc.TotalPrice,
                DeliveryDays = priceCalc.EstimatedDeliveryDays,
                Configuration = itemRequest.Configuration,
                ServiceId = itemRequest.ServiceId,
                ServicePricingTierId = itemRequest.ServicePricingTierId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            quoteItems.Add(quoteItem);
            subtotal += priceCalc.TotalPrice;
            maxDeliveryDays = Math.Max(maxDeliveryDays, priceCalc.EstimatedDeliveryDays);
        }

        quote.Subtotal = subtotal;
        quote.TotalAmount = subtotal; // No tax or discount for now
        quote.EstimatedDeliveryDays = maxDeliveryDays;

        // Save quote
        await _quoteRepository.AddAsync(quote);

        // Save quote items
        foreach (var item in quoteItems)
        {
            item.QuoteId = quote.Id;
            await _quoteItemRepository.AddAsync(item);

            // Save quote item features
            var itemRequest = request.Items.First(i => i.ServiceId == item.ServiceId);
            foreach (var featureRequest in itemRequest.SelectedFeatures)
            {
                var feature = await _serviceFeatureRepository.GetByIdAsync(featureRequest.ServiceFeatureId);
                if (feature == null) continue;

                var quoteItemFeature = new QuoteItemFeature
                {
                    QuoteItemId = item.Id,
                    ServiceFeatureId = featureRequest.ServiceFeatureId,
                    Quantity = featureRequest.Quantity,
                    UnitPrice = feature.AdditionalPrice,
                    TotalPrice = feature.AdditionalPrice * featureRequest.Quantity,
                    Configuration = featureRequest.Configuration,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _quoteItemFeatureRepository.AddAsync(quoteItemFeature);
            }
        }

        return await MapToQuoteResponseDto(quote);
    }

    public async Task<QuoteResponseDto?> GetQuoteAsync(int id)
    {
        var quote = await _quoteRepository.GetWithItemsAndFeaturesAsync(id);
        return quote == null ? null : await MapToQuoteResponseDto(quote);
    }

    public async Task<QuoteResponseDto?> GetQuoteByNumberAsync(string quoteNumber)
    {
        var quote = await _quoteRepository.GetByQuoteNumberAsync(quoteNumber);
        if (quote == null) return null;

        quote = await _quoteRepository.GetWithItemsAndFeaturesAsync(quote.Id);
        return quote == null ? null : await MapToQuoteResponseDto(quote);
    }

    public async Task<bool> UpdateQuoteStatusAsync(int id, string status)
    {
        return await _quoteRepository.UpdateQuoteStatusAsync(id, status);
    }

    public async Task<IEnumerable<ServiceDto>> GetServicesWithPricingAsync()
    {
        var services = await _serviceRepository.ListAsync(s => s.IsActive);
        var result = new List<ServiceDto>();

        foreach (var service in services)
        {
            var serviceDto = await MapToServiceDto(service);
            result.Add(serviceDto);
        }

        return result;
    }

    public async Task<ServiceDto?> GetServiceWithPricingAsync(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        return service == null ? null : await MapToServiceDto(service);
    }

    public async Task<decimal> CalculateQuoteTotalAsync(Quote quote)
    {
        return await _quoteRepository.CalculateQuoteTotalAsync(quote.Id);
    }

    private async Task<ServiceDto> MapToServiceDto(Service service)
    {
        var features = await _serviceFeatureRepository.GetActiveByServiceIdAsync(service.Id);
        var tiers = await _servicePricingTierRepository.GetActiveByServiceIdAsync(service.Id);

        return new ServiceDto
        {
            Id = service.Id,
            Name = service.Name,
            Description = service.Description,
            ShortDescription = service.ShortDescription,
            IconClass = service.IconClass,
            BasePrice = service.BasePrice,
            PricingModel = service.PricingModel,
            ImageUrl = service.ImageUrl,
            Tags = service.Tags,
            IsFeatured = service.IsFeatured,
            EstimatedDeliveryDays = service.EstimatedDeliveryDays,
            Features = features.Select(MapToServiceFeatureDto).ToList(),
            PricingTiers = tiers.Select(MapToServicePricingTierDto).ToList()
        };
    }

    private static ServiceFeatureDto MapToServiceFeatureDto(ServiceFeature feature)
    {
        return new ServiceFeatureDto
        {
            Id = feature.Id,
            Name = feature.Name,
            Description = feature.Description,
            FeatureType = feature.FeatureType,
            AdditionalPrice = feature.AdditionalPrice,
            PricingType = feature.PricingType,
            IconClass = feature.IconClass,
            IsRequired = feature.IsRequired,
            Category = feature.Category
        };
    }

    private static ServicePricingTierDto MapToServicePricingTierDto(ServicePricingTier tier)
    {
        return new ServicePricingTierDto
        {
            Id = tier.Id,
            Name = tier.Name,
            Description = tier.Description,
            Price = tier.Price,
            Features = tier.Features,
            MinQuantity = tier.MinQuantity,
            MaxQuantity = tier.MaxQuantity,
            DeliveryDays = tier.DeliveryDays,
            IsPopular = tier.IsPopular,
            BadgeText = tier.BadgeText,
            BadgeColor = tier.BadgeColor
        };
    }

    private async Task<QuoteResponseDto> MapToQuoteResponseDto(Quote quote)
    {
        var items = new List<QuoteItemDto>();
        
        foreach (var item in quote.Items)
        {
            var service = await MapToServiceDto(item.Service);
            var itemDto = new QuoteItemDto
            {
                Id = item.Id,
                Name = item.Name,
                Description = item.Description,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.TotalPrice,
                DeliveryDays = item.DeliveryDays,
                Service = service,
                ServicePricingTier = item.ServicePricingTier != null 
                    ? MapToServicePricingTierDto(item.ServicePricingTier) 
                    : null,
                SelectedFeatures = item.SelectedFeatures.Select(f => new QuoteItemFeatureDto
                {
                    Id = f.Id,
                    Quantity = f.Quantity,
                    UnitPrice = f.UnitPrice,
                    TotalPrice = f.TotalPrice,
                    ServiceFeature = MapToServiceFeatureDto(f.ServiceFeature)
                }).ToList()
            };
            
            items.Add(itemDto);
        }

        return new QuoteResponseDto
        {
            Id = quote.Id,
            QuoteNumber = quote.QuoteNumber,
            ClientName = quote.ClientName,
            ClientEmail = quote.ClientEmail,
            Subtotal = quote.Subtotal,
            TaxAmount = quote.TaxAmount,
            DiscountAmount = quote.DiscountAmount,
            TotalAmount = quote.TotalAmount,
            Status = quote.Status,
            EstimatedDeliveryDays = quote.EstimatedDeliveryDays,
            CreatedAt = quote.CreatedAt,
            ExpiresAt = quote.ExpiresAt,
            Items = items
        };
    }
}
