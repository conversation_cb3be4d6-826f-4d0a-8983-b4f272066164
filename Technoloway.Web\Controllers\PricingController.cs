using Microsoft.AspNetCore.Mvc;
using Technoloway.Web.Models;
using Technoloway.Web.Services;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Controllers;

public class PricingController : Controller
{
    private readonly IPricingService _pricingService;
    private readonly IHeroSectionRepository _heroSectionRepository;
    private readonly ILogger<PricingController> _logger;

    public PricingController(
        IPricingService pricingService,
        IHeroSectionRepository heroSectionRepository,
        ILogger<PricingController> logger)
    {
        _pricingService = pricingService;
        _heroSectionRepository = heroSectionRepository;
        _logger = logger;
    }

    /// <summary>
    /// Display pricing page with all services
    /// </summary>
    public async Task<IActionResult> Index(string? category = null)
    {
        try
        {
            _logger.LogInformation("Loading pricing page with category: {Category}", category);

            // Simple test - return a basic view first
            var viewModel = new PricingIndexViewModel
            {
                Services = new List<ServiceDto>(),
                FeaturedServices = new List<ServiceDto>(),
                SelectedCategory = category,
                Categories = new List<string>()
            };

            ViewBag.HeroSection = null;
            ViewBag.PageName = "Pricing";
            ViewBag.TestMessage = "Pricing page loaded successfully!";

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading pricing page");
            throw; // Re-throw to see the actual error in development
        }
    }

    /// <summary>
    /// Display service configuration page
    /// </summary>
    public async Task<IActionResult> Configure(int id)
    {
        try
        {
            var service = await _pricingService.GetServiceWithPricingAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            var viewModel = new ServiceConfigurationViewModel
            {
                Service = service,
                Quantity = 1,
                SelectedFeatures = service.Features.ToDictionary(f => f.Id, f => f.IsRequired),
                FeatureQuantities = service.Features.ToDictionary(f => f.Id, f => 1)
            };

            // Calculate initial price
            var priceRequest = new PriceCalculationRequest
            {
                ServiceId = service.Id,
                Quantity = 1,
                SelectedFeatureIds = service.Features.Where(f => f.IsRequired).Select(f => f.Id).ToList()
            };

            var priceResponse = await _pricingService.CalculatePriceAsync(priceRequest);
            viewModel.CalculatedPrice = priceResponse.TotalPrice;
            viewModel.EstimatedDeliveryDays = priceResponse.EstimatedDeliveryDays;

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading service configuration for service {ServiceId}", id);
            return View("Error");
        }
    }

    /// <summary>
    /// Display quote builder page
    /// </summary>
    public IActionResult QuoteBuilder()
    {
        var viewModel = new QuoteBuilderViewModel();
        return View(viewModel);
    }

    /// <summary>
    /// Submit quote request
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> SubmitQuote([FromBody] QuoteRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var quote = await _pricingService.CreateQuoteAsync(request);
            return Json(new { success = true, quoteId = quote.Id, quoteNumber = quote.QuoteNumber });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting quote for {ClientEmail}", request.ClientEmail);
            return Json(new { success = false, message = "An error occurred while submitting your quote request." });
        }
    }

    /// <summary>
    /// Display quote details
    /// </summary>
    public async Task<IActionResult> Quote(int id)
    {
        try
        {
            var quote = await _pricingService.GetQuoteAsync(id);
            if (quote == null)
            {
                return NotFound();
            }

            return View(quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading quote {QuoteId}", id);
            return View("Error");
        }
    }

    /// <summary>
    /// Display quote by quote number (public access)
    /// </summary>
    public async Task<IActionResult> QuoteByNumber(string number)
    {
        try
        {
            var quote = await _pricingService.GetQuoteByNumberAsync(number);
            if (quote == null)
            {
                return NotFound();
            }

            return View("Quote", quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading quote {QuoteNumber}", number);
            return View("Error");
        }
    }

    /// <summary>
    /// AJAX endpoint for price calculation
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CalculatePrice([FromBody] PriceCalculationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _pricingService.CalculatePriceAsync(request);
            return Json(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating price for service {ServiceId}", request.ServiceId);
            return StatusCode(500, new { message = "An error occurred while calculating the price" });
        }
    }

    /// <summary>
    /// Get service details for AJAX requests
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetService(int id)
    {
        try
        {
            var service = await _pricingService.GetServiceWithPricingAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            return Json(service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service {ServiceId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the service" });
        }
    }
}
