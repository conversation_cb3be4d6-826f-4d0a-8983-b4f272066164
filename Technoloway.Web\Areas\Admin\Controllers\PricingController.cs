using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize]
public class PricingController : Controller
{
    private readonly IRepository<Service> _serviceRepository;
    private readonly IServiceFeatureRepository _serviceFeatureRepository;
    private readonly IServicePricingTierRepository _servicePricingTierRepository;
    private readonly IQuoteRepository _quoteRepository;
    private readonly IPricingService _pricingService;
    private readonly ILogger<PricingController> _logger;

    public PricingController(
        IRepository<Service> serviceRepository,
        IServiceFeatureRepository serviceFeatureRepository,
        IServicePricingTierRepository servicePricingTierRepository,
        IQuoteRepository quoteRepository,
        IPricingService pricingService,
        ILogger<PricingController> logger)
    {
        _serviceRepository = serviceRepository;
        _serviceFeatureRepository = serviceFeatureRepository;
        _servicePricingTierRepository = servicePricingTierRepository;
        _quoteRepository = quoteRepository;
        _pricingService = pricingService;
        _logger = logger;
    }

    // Service Features Management
    public async Task<IActionResult> ServiceFeatures(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            return NotFound();
        }

        var features = await _serviceFeatureRepository.GetByServiceIdAsync(serviceId);
        ViewBag.Service = service;
        return View(features);
    }

    public async Task<IActionResult> CreateFeature(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            return NotFound();
        }

        var feature = new ServiceFeature { ServiceId = serviceId };
        ViewBag.Service = service;
        return View(feature);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateFeature(ServiceFeature feature)
    {
        if (ModelState.IsValid)
        {
            try
            {
                feature.CreatedAt = DateTime.UtcNow;
                feature.UpdatedAt = DateTime.UtcNow;
                await _serviceFeatureRepository.AddAsync(feature);
                TempData["Success"] = "Service feature created successfully.";
                return RedirectToAction(nameof(ServiceFeatures), new { serviceId = feature.ServiceId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service feature");
                ModelState.AddModelError("", "An error occurred while creating the service feature.");
            }
        }

        var service = await _serviceRepository.GetByIdAsync(feature.ServiceId);
        ViewBag.Service = service;
        return View(feature);
    }

    public async Task<IActionResult> EditFeature(int id)
    {
        var feature = await _serviceFeatureRepository.GetByIdAsync(id);
        if (feature == null)
        {
            return NotFound();
        }

        var service = await _serviceRepository.GetByIdAsync(feature.ServiceId);
        ViewBag.Service = service;
        return View(feature);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditFeature(ServiceFeature feature)
    {
        if (ModelState.IsValid)
        {
            try
            {
                await _serviceFeatureRepository.UpdateAsync(feature);
                TempData["Success"] = "Service feature updated successfully.";
                return RedirectToAction(nameof(ServiceFeatures), new { serviceId = feature.ServiceId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service feature {FeatureId}", feature.Id);
                ModelState.AddModelError("", "An error occurred while updating the service feature.");
            }
        }

        var service = await _serviceRepository.GetByIdAsync(feature.ServiceId);
        ViewBag.Service = service;
        return View(feature);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteFeature(int id)
    {
        try
        {
            var feature = await _serviceFeatureRepository.GetByIdAsync(id);
            if (feature == null)
            {
                return NotFound();
            }

            var serviceId = feature.ServiceId;
            await _serviceFeatureRepository.DeleteAsync(feature);
            TempData["Success"] = "Service feature deleted successfully.";
            return RedirectToAction(nameof(ServiceFeatures), new { serviceId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting service feature {FeatureId}", id);
            TempData["Error"] = "An error occurred while deleting the service feature.";
            return RedirectToAction(nameof(ServiceFeatures));
        }
    }

    // Pricing Tiers Management
    public async Task<IActionResult> PricingTiers(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            return NotFound();
        }

        var tiers = await _servicePricingTierRepository.GetByServiceIdAsync(serviceId);
        ViewBag.Service = service;
        return View(tiers);
    }

    public async Task<IActionResult> CreateTier(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            return NotFound();
        }

        var tier = new ServicePricingTier { ServiceId = serviceId };
        ViewBag.Service = service;
        return View(tier);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateTier(ServicePricingTier tier)
    {
        if (ModelState.IsValid)
        {
            try
            {
                tier.CreatedAt = DateTime.UtcNow;
                tier.UpdatedAt = DateTime.UtcNow;
                await _servicePricingTierRepository.AddAsync(tier);
                TempData["Success"] = "Pricing tier created successfully.";
                return RedirectToAction(nameof(PricingTiers), new { serviceId = tier.ServiceId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating pricing tier");
                ModelState.AddModelError("", "An error occurred while creating the pricing tier.");
            }
        }

        var service = await _serviceRepository.GetByIdAsync(tier.ServiceId);
        ViewBag.Service = service;
        return View(tier);
    }

    public async Task<IActionResult> EditTier(int id)
    {
        var tier = await _servicePricingTierRepository.GetByIdAsync(id);
        if (tier == null)
        {
            return NotFound();
        }

        var service = await _serviceRepository.GetByIdAsync(tier.ServiceId);
        ViewBag.Service = service;
        return View(tier);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditTier(ServicePricingTier tier)
    {
        if (ModelState.IsValid)
        {
            try
            {
                await _servicePricingTierRepository.UpdateAsync(tier);
                TempData["Success"] = "Pricing tier updated successfully.";
                return RedirectToAction(nameof(PricingTiers), new { serviceId = tier.ServiceId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating pricing tier {TierId}", tier.Id);
                ModelState.AddModelError("", "An error occurred while updating the pricing tier.");
            }
        }

        var service = await _serviceRepository.GetByIdAsync(tier.ServiceId);
        ViewBag.Service = service;
        return View(tier);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteTier(int id)
    {
        try
        {
            var tier = await _servicePricingTierRepository.GetByIdAsync(id);
            if (tier == null)
            {
                return NotFound();
            }

            var serviceId = tier.ServiceId;
            await _servicePricingTierRepository.DeleteAsync(tier);
            TempData["Success"] = "Pricing tier deleted successfully.";
            return RedirectToAction(nameof(PricingTiers), new { serviceId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting pricing tier {TierId}", id);
            TempData["Error"] = "An error occurred while deleting the pricing tier.";
            return RedirectToAction(nameof(PricingTiers));
        }
    }

    // Quotes Management
    public async Task<IActionResult> Quotes(string? status = null)
    {
        try
        {
            var quotes = string.IsNullOrEmpty(status)
                ? await _quoteRepository.ListAllAsync()
                : await _quoteRepository.GetByStatusAsync(status);

            ViewBag.SelectedStatus = status;
            ViewBag.StatusCounts = await _quoteRepository.GetQuoteStatusCountsAsync();
            return View(quotes.OrderByDescending(q => q.CreatedAt));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading quotes");
            return View("Error");
        }
    }

    public async Task<IActionResult> QuoteDetails(int id)
    {
        try
        {
            var quote = await _pricingService.GetQuoteAsync(id);
            if (quote == null)
            {
                return NotFound();
            }

            return View(quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading quote details {QuoteId}", id);
            return View("Error");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateQuoteStatus(int id, string status)
    {
        try
        {
            var success = await _pricingService.UpdateQuoteStatusAsync(id, status);
            if (success)
            {
                TempData["Success"] = "Quote status updated successfully.";
            }
            else
            {
                TempData["Error"] = "Quote not found.";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quote status {QuoteId}", id);
            TempData["Error"] = "An error occurred while updating the quote status.";
        }

        return RedirectToAction(nameof(QuoteDetails), new { id });
    }

    // Analytics
    public async Task<IActionResult> Analytics()
    {
        try
        {
            var statusCounts = await _quoteRepository.GetQuoteStatusCountsAsync();
            var totalValue = await _quoteRepository.GetTotalQuoteValueByStatusAsync("Accepted");
            var recentQuotes = await _quoteRepository.GetQuotesByDateRangeAsync(
                DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);

            ViewBag.StatusCounts = statusCounts;
            ViewBag.TotalAcceptedValue = totalValue;
            ViewBag.RecentQuotesCount = recentQuotes.Count();
            
            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading pricing analytics");
            return View("Error");
        }
    }
}
