using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using System.Linq;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class HomeController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Core.Entities.Client> _clientRepository;
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly IRepository<ContactForm> _contactFormRepository;
    private readonly IRepository<Payment> _paymentRepository;
    private readonly IRepository<JobApplication> _jobApplicationRepository;
    private readonly IRepository<Testimonial> _testimonialRepository;
    private readonly IRepository<BlogPost> _blogPostRepository;

    public HomeController(
        IRepository<Project> projectRepository,
        IRepository<Core.Entities.Client> clientRepository,
        IRepository<Invoice> invoiceRepository,
        IRepository<ContactForm> contactFormRepository,
        IRepository<Payment> paymentRepository,
        IRepository<JobApplication> jobApplicationRepository,
        IRepository<Testimonial> testimonialRepository,
        IRepository<BlogPost> blogPostRepository)
    {
        _projectRepository = projectRepository;
        _clientRepository = clientRepository;
        _invoiceRepository = invoiceRepository;
        _contactFormRepository = contactFormRepository;
        _paymentRepository = paymentRepository;
        _jobApplicationRepository = jobApplicationRepository;
        _testimonialRepository = testimonialRepository;
        _blogPostRepository = blogPostRepository;
    }

    public async Task<IActionResult> Index()
    {
        // Basic counts
        var projectCount = await _projectRepository.CountAsync();
        var clientCount = await _clientRepository.CountAsync();
        var invoiceCount = await _invoiceRepository.CountAsync();
        var pendingInvoiceCount = await _invoiceRepository.CountAsync(i => i.Status == "Pending");
        var unreadContactCount = await _contactFormRepository.CountAsync(c => !c.IsRead);
        var jobApplicationCount = await _jobApplicationRepository.CountAsync();
        var testimonialCount = await _testimonialRepository.CountAsync();
        var blogPostCount = await _blogPostRepository.CountAsync();

        // Revenue calculations
        var allInvoices = await _invoiceRepository.ListAsync(i => !i.IsDeleted);
        var totalRevenue = allInvoices.Sum(i => i.TotalAmount);
        var paidRevenue = allInvoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount);
        var pendingRevenue = allInvoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount);
        var overdueRevenue = allInvoices.Where(i => i.Status == "Overdue").Sum(i => i.TotalAmount);

        // Monthly comparisons
        var currentMonth = DateTime.UtcNow.Month;
        var currentYear = DateTime.UtcNow.Year;
        var lastMonth = currentMonth == 1 ? 12 : currentMonth - 1;
        var lastMonthYear = currentMonth == 1 ? currentYear - 1 : currentYear;

        var currentMonthRevenue = allInvoices
            .Where(i => i.CreatedAt.Month == currentMonth && i.CreatedAt.Year == currentYear)
            .Sum(i => i.TotalAmount);
        var lastMonthRevenue = allInvoices
            .Where(i => i.CreatedAt.Month == lastMonth && i.CreatedAt.Year == lastMonthYear)
            .Sum(i => i.TotalAmount);

        var revenueGrowth = lastMonthRevenue > 0 ?
            ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

        // Project status distribution
        var allProjects = await _projectRepository.ListAsync(p => !p.IsDeleted);
        var completedProjects = allProjects.Count(p => p.CompletionDate <= DateTime.UtcNow);
        var activeProjects = allProjects.Count(p => p.CompletionDate > DateTime.UtcNow);

        // Recent activity data
        var recentProjects = allProjects.OrderByDescending(p => p.CreatedAt).Take(5).ToList();
        var recentInvoices = allInvoices.OrderByDescending(i => i.CreatedAt).Take(5).ToList();
        var allClients = await _clientRepository.ListAsync(c => !c.IsDeleted);
        var recentClients = allClients.OrderByDescending(c => c.CreatedAt).Take(5).ToList();

        // Chart data for revenue trends (last 6 months)
        var revenueChartData = new List<object>();
        for (int i = 5; i >= 0; i--)
        {
            var date = DateTime.UtcNow.AddMonths(-i);
            var monthRevenue = allInvoices
                .Where(inv => inv.CreatedAt.Month == date.Month && inv.CreatedAt.Year == date.Year)
                .Sum(inv => inv.TotalAmount);
            revenueChartData.Add(new {
                month = date.ToString("MMM yyyy"),
                revenue = monthRevenue
            });
        }

        // Project status chart data
        var projectStatusData = new List<object>
        {
            new { status = "Completed", count = completedProjects },
            new { status = "Active", count = activeProjects }
        };

        // Pass data to view
        ViewBag.ProjectCount = projectCount;
        ViewBag.ClientCount = clientCount;
        ViewBag.InvoiceCount = invoiceCount;
        ViewBag.PendingInvoiceCount = pendingInvoiceCount;
        ViewBag.UnreadContactCount = unreadContactCount;
        ViewBag.JobApplicationCount = jobApplicationCount;
        ViewBag.TestimonialCount = testimonialCount;
        ViewBag.BlogPostCount = blogPostCount;

        ViewBag.TotalRevenue = totalRevenue;
        ViewBag.PaidRevenue = paidRevenue;
        ViewBag.PendingRevenue = pendingRevenue;
        ViewBag.OverdueRevenue = overdueRevenue;
        ViewBag.RevenueGrowth = revenueGrowth;

        ViewBag.CompletedProjects = completedProjects;
        ViewBag.ActiveProjects = activeProjects;

        ViewBag.RecentProjects = recentProjects;
        ViewBag.RecentInvoices = recentInvoices;
        ViewBag.RecentClients = recentClients;

        ViewBag.RevenueChartData = System.Text.Json.JsonSerializer.Serialize(revenueChartData);
        ViewBag.ProjectStatusData = System.Text.Json.JsonSerializer.Serialize(projectStatusData);

        return View();
    }

    [HttpGet]
    public async Task<IActionResult> Search(string query)
    {
        if (string.IsNullOrWhiteSpace(query) || query.Length < 2)
        {
            return Json(new { results = new List<object>() });
        }

        var results = new List<object>();

        try
        {
            // Search Projects
            var projects = await _projectRepository.GetAll()
                .Where(p => !p.IsDeleted &&
                           (p.Name.Contains(query) || p.Description.Contains(query) || p.ClientName.Contains(query)))
                .Take(5)
                .Select(p => new
                {
                    type = "project",
                    title = p.Name,
                    subtitle = p.ClientName,
                    url = Url.Action("Details", "Projects", new { area = "Admin", id = p.Id }),
                    id = p.Id
                })
                .ToListAsync();

            results.AddRange(projects);

            // Search Clients
            var clients = await _clientRepository.GetAll()
                .Where(c => !c.IsDeleted &&
                           (c.ContactName.Contains(query) || c.ContactEmail.Contains(query) || c.CompanyName.Contains(query)))
                .Take(5)
                .Select(c => new
                {
                    type = "client",
                    title = c.ContactName,
                    subtitle = c.CompanyName ?? c.ContactEmail,
                    url = Url.Action("Details", "Clients", new { area = "Admin", id = c.Id }),
                    id = c.Id
                })
                .ToListAsync();

            results.AddRange(clients);

            // Search Invoices
            var invoices = await _invoiceRepository.GetAll()
                .Include(i => i.Client)
                .Where(i => !i.IsDeleted &&
                           (i.InvoiceNumber.Contains(query) ||
                            (i.Client != null && i.Client.ContactName.Contains(query))))
                .Take(5)
                .Select(i => new
                {
                    type = "invoice",
                    title = i.InvoiceNumber,
                    subtitle = i.Client != null ? i.Client.ContactName : "Unknown Client",
                    url = Url.Action("Details", "Invoices", new { area = "Admin", id = i.Id }),
                    id = i.Id
                })
                .ToListAsync();

            results.AddRange(invoices);

            return Json(new { results = results.Take(10) });
        }
        catch (Exception ex)
        {
            return Json(new { error = "Search failed", message = ex.Message });
        }
    }
}
