using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using System.Linq;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class HomeController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Core.Entities.Client> _clientRepository;
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly IRepository<ContactForm> _contactFormRepository;
    private readonly IRepository<Payment> _paymentRepository;
    private readonly IRepository<JobApplication> _jobApplicationRepository;
    private readonly IRepository<Testimonial> _testimonialRepository;
    private readonly IRepository<BlogPost> _blogPostRepository;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IRepository<TeamMember> _teamMemberRepository;
    private readonly IRepository<JobListing> _jobListingRepository;
    private readonly IRepository<Message> _messageRepository;
    private readonly IRepository<ProjectDocument> _projectDocumentRepository;
    private readonly IRepository<SiteSetting> _siteSettingRepository;
    private readonly IRepository<Feedback> _feedbackRepository;
    private readonly IRepository<LegalPage> _legalPageRepository;
    private readonly IRepository<LegalPageSection> _legalPageSectionRepository;
    private readonly IRepository<AboutPage> _aboutPageRepository;
    private readonly IRepository<AboutPageSection> _aboutPageSectionRepository;
    private readonly IRepository<HeroSection> _heroSectionRepository;
    private readonly IRepository<HeroSlide> _heroSlideRepository;
    private readonly IRepository<ChatbotIntent> _chatbotIntentRepository;
    private readonly IRepository<ChatbotResponse> _chatbotResponseRepository;
    private readonly IRepository<ChatbotKeyword> _chatbotKeywordRepository;
    private readonly IRepository<ChatbotQuickAction> _chatbotQuickActionRepository;
    private readonly IRepository<InvoiceItem> _invoiceItemRepository;
    private readonly IQuoteRepository _quoteRepository;

    public HomeController(
        IRepository<Project> projectRepository,
        IRepository<Core.Entities.Client> clientRepository,
        IRepository<Invoice> invoiceRepository,
        IRepository<ContactForm> contactFormRepository,
        IRepository<Payment> paymentRepository,
        IRepository<JobApplication> jobApplicationRepository,
        IRepository<Testimonial> testimonialRepository,
        IRepository<BlogPost> blogPostRepository,
        IRepository<Service> serviceRepository,
        IRepository<Technology> technologyRepository,
        IRepository<TeamMember> teamMemberRepository,
        IRepository<JobListing> jobListingRepository,
        IRepository<Message> messageRepository,
        IRepository<ProjectDocument> projectDocumentRepository,
        IRepository<SiteSetting> siteSettingRepository,
        IRepository<Feedback> feedbackRepository,
        IRepository<LegalPage> legalPageRepository,
        IRepository<LegalPageSection> legalPageSectionRepository,
        IRepository<AboutPage> aboutPageRepository,
        IRepository<AboutPageSection> aboutPageSectionRepository,
        IRepository<HeroSection> heroSectionRepository,
        IRepository<HeroSlide> heroSlideRepository,
        IRepository<ChatbotIntent> chatbotIntentRepository,
        IRepository<ChatbotResponse> chatbotResponseRepository,
        IRepository<ChatbotKeyword> chatbotKeywordRepository,
        IRepository<ChatbotQuickAction> chatbotQuickActionRepository,
        IRepository<InvoiceItem> invoiceItemRepository,
        IQuoteRepository quoteRepository)
    {
        _projectRepository = projectRepository;
        _clientRepository = clientRepository;
        _invoiceRepository = invoiceRepository;
        _contactFormRepository = contactFormRepository;
        _paymentRepository = paymentRepository;
        _jobApplicationRepository = jobApplicationRepository;
        _testimonialRepository = testimonialRepository;
        _blogPostRepository = blogPostRepository;
        _serviceRepository = serviceRepository;
        _technologyRepository = technologyRepository;
        _teamMemberRepository = teamMemberRepository;
        _jobListingRepository = jobListingRepository;
        _messageRepository = messageRepository;
        _projectDocumentRepository = projectDocumentRepository;
        _siteSettingRepository = siteSettingRepository;
        _feedbackRepository = feedbackRepository;
        _legalPageRepository = legalPageRepository;
        _legalPageSectionRepository = legalPageSectionRepository;
        _aboutPageRepository = aboutPageRepository;
        _aboutPageSectionRepository = aboutPageSectionRepository;
        _heroSectionRepository = heroSectionRepository;
        _heroSlideRepository = heroSlideRepository;
        _chatbotIntentRepository = chatbotIntentRepository;
        _chatbotResponseRepository = chatbotResponseRepository;
        _chatbotKeywordRepository = chatbotKeywordRepository;
        _chatbotQuickActionRepository = chatbotQuickActionRepository;
        _invoiceItemRepository = invoiceItemRepository;
        _quoteRepository = quoteRepository;
    }

    public async Task<IActionResult> Index()
    {
        // Basic counts
        var projectCount = await _projectRepository.CountAsync();
        var clientCount = await _clientRepository.CountAsync();
        var invoiceCount = await _invoiceRepository.CountAsync();
        var pendingInvoiceCount = await _invoiceRepository.CountAsync(i => i.Status == "Pending");
        var unreadContactCount = await _contactFormRepository.CountAsync(c => !c.IsRead);
        var jobApplicationCount = await _jobApplicationRepository.CountAsync();
        var testimonialCount = await _testimonialRepository.CountAsync();
        var blogPostCount = await _blogPostRepository.CountAsync();

        // Quote statistics
        var totalQuotes = await _quoteRepository.CountAsync();
        var pendingQuotes = await _quoteRepository.CountAsync(q => q.Status == "Sent");
        var acceptedQuotes = await _quoteRepository.CountAsync(q => q.Status == "Accepted");
        var quotesValue = await _quoteRepository.GetTotalQuoteValueByStatusAsync("Accepted");

        // Revenue calculations
        var allInvoices = await _invoiceRepository.ListAsync(i => !i.IsDeleted);
        var totalRevenue = allInvoices.Sum(i => i.TotalAmount);
        var paidRevenue = allInvoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount);
        var pendingRevenue = allInvoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount);
        var overdueRevenue = allInvoices.Where(i => i.Status == "Overdue").Sum(i => i.TotalAmount);

        // Monthly comparisons
        var currentMonth = DateTime.UtcNow.Month;
        var currentYear = DateTime.UtcNow.Year;
        var lastMonth = currentMonth == 1 ? 12 : currentMonth - 1;
        var lastMonthYear = currentMonth == 1 ? currentYear - 1 : currentYear;

        var currentMonthRevenue = allInvoices
            .Where(i => i.CreatedAt.Month == currentMonth && i.CreatedAt.Year == currentYear)
            .Sum(i => i.TotalAmount);
        var lastMonthRevenue = allInvoices
            .Where(i => i.CreatedAt.Month == lastMonth && i.CreatedAt.Year == lastMonthYear)
            .Sum(i => i.TotalAmount);

        var revenueGrowth = lastMonthRevenue > 0 ?
            ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

        // Project status distribution
        var allProjects = await _projectRepository.ListAsync(p => !p.IsDeleted);
        var completedProjects = allProjects.Count(p => p.CompletionDate <= DateTime.UtcNow);
        var activeProjects = allProjects.Count(p => p.CompletionDate > DateTime.UtcNow);

        // Recent activity data
        var recentProjects = allProjects.OrderByDescending(p => p.CreatedAt).Take(5).ToList();
        var recentInvoices = allInvoices.OrderByDescending(i => i.CreatedAt).Take(5).ToList();
        var allClients = await _clientRepository.ListAsync(c => !c.IsDeleted);
        var recentClients = allClients.OrderByDescending(c => c.CreatedAt).Take(5).ToList();

        // Chart data for revenue trends (last 6 months)
        var revenueChartData = new List<object>();
        for (int i = 5; i >= 0; i--)
        {
            var date = DateTime.UtcNow.AddMonths(-i);
            var monthRevenue = allInvoices
                .Where(inv => inv.CreatedAt.Month == date.Month && inv.CreatedAt.Year == date.Year)
                .Sum(inv => inv.TotalAmount);
            revenueChartData.Add(new {
                month = date.ToString("MMM yyyy"),
                revenue = monthRevenue
            });
        }

        // Project status chart data
        var projectStatusData = new List<object>
        {
            new { status = "Completed", count = completedProjects },
            new { status = "Active", count = activeProjects }
        };

        // Pass data to view
        ViewBag.ProjectCount = projectCount;
        ViewBag.ClientCount = clientCount;
        ViewBag.InvoiceCount = invoiceCount;
        ViewBag.PendingInvoiceCount = pendingInvoiceCount;
        ViewBag.UnreadContactCount = unreadContactCount;
        ViewBag.JobApplicationCount = jobApplicationCount;
        ViewBag.TestimonialCount = testimonialCount;
        ViewBag.BlogPostCount = blogPostCount;

        ViewBag.TotalQuotes = totalQuotes;
        ViewBag.PendingQuotes = pendingQuotes;
        ViewBag.AcceptedQuotes = acceptedQuotes;
        ViewBag.QuotesValue = quotesValue;

        ViewBag.TotalRevenue = totalRevenue;
        ViewBag.PaidRevenue = paidRevenue;
        ViewBag.PendingRevenue = pendingRevenue;
        ViewBag.OverdueRevenue = overdueRevenue;
        ViewBag.RevenueGrowth = revenueGrowth;

        ViewBag.CompletedProjects = completedProjects;
        ViewBag.ActiveProjects = activeProjects;

        ViewBag.RecentProjects = recentProjects;
        ViewBag.RecentInvoices = recentInvoices;
        ViewBag.RecentClients = recentClients;

        ViewBag.RevenueChartData = System.Text.Json.JsonSerializer.Serialize(revenueChartData);
        ViewBag.ProjectStatusData = System.Text.Json.JsonSerializer.Serialize(projectStatusData);

        return View();
    }

    [HttpGet]
    public async Task<IActionResult> Search(string query)
    {
        if (string.IsNullOrWhiteSpace(query) || query.Length < 1)
        {
            return Json(new { results = new List<object>() });
        }

        var results = new List<object>();

        try
        {
            // Search Projects - ALL fields
            var projects = await _projectRepository.GetAll()
                .Include(p => p.Client)
                .Include(p => p.Service)
                .Include(p => p.Technologies)
                .Where(p => !p.IsDeleted &&
                           (p.Name.Contains(query) ||
                            p.Description.Contains(query) ||
                            p.ClientName.Contains(query) ||
                            p.ProjectUrl.Contains(query) ||
                            p.ImageUrl.Contains(query) ||
                            (p.Client != null && (p.Client.ContactName.Contains(query) ||
                                                  p.Client.CompanyName.Contains(query) ||
                                                  p.Client.ContactEmail.Contains(query) ||
                                                  p.Client.ContactPhone.Contains(query) ||
                                                  p.Client.Address.Contains(query) ||
                                                  p.Client.City.Contains(query) ||
                                                  p.Client.State.Contains(query) ||
                                                  p.Client.Country.Contains(query) ||
                                                  p.Client.ZipCode.Contains(query))) ||
                            (p.Service != null && (p.Service.Name.Contains(query) || p.Service.Description.Contains(query))) ||
                            p.Technologies.Any(t => t.Name.Contains(query) || t.Description.Contains(query))))
                .Take(3)
                .Select(p => new
                {
                    type = "project",
                    title = p.Name,
                    subtitle = p.Client != null ? p.Client.CompanyName : p.ClientName,
                    url = Url.Action("Details", "Projects", new { area = "Admin", id = p.Id }),
                    id = p.Id,
                    description = p.Description.Length > 80 ? p.Description.Substring(0, 80) + "..." : p.Description
                })
                .ToListAsync();

            results.AddRange(projects);

            // Search Clients - ALL fields
            var clients = await _clientRepository.GetAll()
                .Where(c => !c.IsDeleted &&
                           (c.ContactName.Contains(query) ||
                            c.ContactEmail.Contains(query) ||
                            c.CompanyName.Contains(query) ||
                            c.ContactPhone.Contains(query) ||
                            c.Address.Contains(query) ||
                            c.City.Contains(query) ||
                            c.State.Contains(query) ||
                            c.Country.Contains(query) ||
                            c.ZipCode.Contains(query)))
                .Take(3)
                .Select(c => new
                {
                    type = "client",
                    title = c.ContactName,
                    subtitle = c.CompanyName ?? c.ContactEmail,
                    url = Url.Action("Details", "Clients", new { area = "Admin", id = c.Id }),
                    id = c.Id,
                    description = $"{c.ContactEmail} • {c.City}, {c.State}"
                })
                .ToListAsync();

            results.AddRange(clients);

            // Search Invoices - ALL fields
            var invoices = await _invoiceRepository.GetAll()
                .Include(i => i.Client)
                .Include(i => i.Project)
                .Include(i => i.Items)
                .Where(i => !i.IsDeleted &&
                           (i.InvoiceNumber.Contains(query) ||
                            i.Status.Contains(query) ||
                            i.Notes.Contains(query) ||
                            i.Amount.ToString().Contains(query) ||
                            i.TotalAmount.ToString().Contains(query) ||
                            i.TaxAmount.ToString().Contains(query) ||
                            (i.Client != null && (i.Client.ContactName.Contains(query) ||
                                                  i.Client.CompanyName.Contains(query) ||
                                                  i.Client.ContactEmail.Contains(query))) ||
                            (i.Project != null && i.Project.Name.Contains(query)) ||
                            i.Items.Any(item => item.Description.Contains(query))))
                .Take(3)
                .Select(i => new
                {
                    type = "invoice",
                    title = i.InvoiceNumber,
                    subtitle = i.Client != null ? i.Client.CompanyName : "Unknown Client",
                    url = Url.Action("Details", "Invoices", new { area = "Admin", id = i.Id }),
                    id = i.Id,
                    description = $"{i.Status} • ${i.TotalAmount:F2} • {i.IssueDate:MMM dd, yyyy}"
                })
                .ToListAsync();

            results.AddRange(invoices);

            // Search Services - ALL fields
            var services = await _serviceRepository.GetAll()
                .Where(s => !s.IsDeleted &&
                           (s.Name.Contains(query) ||
                            s.Description.Contains(query) ||
                            s.IconClass.Contains(query) ||
                            s.Price.ToString().Contains(query)))
                .Take(2)
                .Select(s => new
                {
                    type = "service",
                    title = s.Name,
                    subtitle = $"${s.Price:F2}",
                    url = Url.Action("Details", "Services", new { area = "Admin", id = s.Id }),
                    id = s.Id,
                    description = s.Description.Length > 80 ? s.Description.Substring(0, 80) + "..." : s.Description
                })
                .ToListAsync();

            results.AddRange(services);

            // Search Blog Posts - ALL fields
            var blogPosts = await _blogPostRepository.GetAll()
                .Where(b => !b.IsDeleted &&
                           (b.Title.Contains(query) ||
                            b.Content.Contains(query) ||
                            b.Excerpt.Contains(query) ||
                            b.Slug.Contains(query) ||
                            b.Categories.Contains(query) ||
                            b.MetaTitle.Contains(query) ||
                            b.MetaDescription.Contains(query) ||
                            b.MetaKeywords.Contains(query)))
                .Take(2)
                .Select(b => new
                {
                    type = "blog",
                    title = b.Title,
                    subtitle = b.IsPublished ? "Published" : "Draft",
                    url = Url.Action("Details", "Blog", new { area = "Admin", id = b.Id }),
                    id = b.Id,
                    description = b.Excerpt.Length > 80 ? b.Excerpt.Substring(0, 80) + "..." : b.Excerpt
                })
                .ToListAsync();

            results.AddRange(blogPosts);

            // Search Testimonials - ALL fields
            var testimonials = await _testimonialRepository.GetAll()
                .Where(t => !t.IsDeleted &&
                           (t.ClientName.Contains(query) ||
                            t.ClientCompany.Contains(query) ||
                            t.ClientTitle.Contains(query) ||
                            t.Content.Contains(query)))
                .Take(2)
                .Select(t => new
                {
                    type = "testimonial",
                    title = t.ClientName,
                    subtitle = $"{t.ClientTitle} at {t.ClientCompany}",
                    url = Url.Action("Details", "Testimonials", new { area = "Admin", id = t.Id }),
                    id = t.Id,
                    description = t.Content.Length > 80 ? t.Content.Substring(0, 80) + "..." : t.Content
                })
                .ToListAsync();

            results.AddRange(testimonials);

            // Search Technologies - ALL fields
            var technologies = await _technologyRepository.GetAll()
                .Where(t => !t.IsDeleted &&
                           (t.Name.Contains(query) ||
                            t.Description.Contains(query) ||
                            t.IconUrl.Contains(query)))
                .Take(2)
                .Select(t => new
                {
                    type = "technology",
                    title = t.Name,
                    subtitle = "Technology",
                    url = Url.Action("Details", "Technologies", new { area = "Admin", id = t.Id }),
                    id = t.Id,
                    description = t.Description.Length > 80 ? t.Description.Substring(0, 80) + "..." : t.Description
                })
                .ToListAsync();

            results.AddRange(technologies);

            // Search Team Members - ALL fields
            var teamMembers = await _teamMemberRepository.GetAll()
                .Where(tm => !tm.IsDeleted &&
                            (tm.Name.Contains(query) ||
                             tm.Position.Contains(query) ||
                             tm.Bio.Contains(query) ||
                             tm.Email.Contains(query) ||
                             tm.LinkedInUrl.Contains(query) ||
                             tm.TwitterUrl.Contains(query) ||
                             tm.GithubUrl.Contains(query)))
                .Take(2)
                .Select(tm => new
                {
                    type = "team",
                    title = tm.Name,
                    subtitle = tm.Position,
                    url = Url.Action("Details", "TeamMembers", new { area = "Admin", id = tm.Id }),
                    id = tm.Id,
                    description = tm.Bio.Length > 80 ? tm.Bio.Substring(0, 80) + "..." : tm.Bio
                })
                .ToListAsync();

            results.AddRange(teamMembers);

            // Search Job Listings - ALL fields
            var jobListings = await _jobListingRepository.GetAll()
                .Where(jl => !jl.IsDeleted &&
                            (jl.Title.Contains(query) ||
                             jl.Description.Contains(query) ||
                             jl.Requirements.Contains(query) ||
                             jl.Location.Contains(query) ||
                             jl.EmploymentType.Contains(query) ||
                             jl.SalaryCurrency.Contains(query) ||
                             (jl.SalaryMin.HasValue && jl.SalaryMin.ToString().Contains(query)) ||
                             (jl.SalaryMax.HasValue && jl.SalaryMax.ToString().Contains(query))))
                .Take(2)
                .Select(jl => new
                {
                    type = "job",
                    title = jl.Title,
                    subtitle = $"{jl.Location} • {jl.EmploymentType}",
                    url = Url.Action("Details", "Jobs", new { area = "Admin", id = jl.Id }),
                    id = jl.Id,
                    description = jl.Description.Length > 80 ? jl.Description.Substring(0, 80) + "..." : jl.Description
                })
                .ToListAsync();

            results.AddRange(jobListings);

            // Search Job Applications - ALL fields
            var jobApplications = await _jobApplicationRepository.GetAll()
                .Include(ja => ja.JobListing)
                .Where(ja => !ja.IsDeleted &&
                            (ja.ApplicantName.Contains(query) ||
                             ja.ApplicantEmail.Contains(query) ||
                             ja.ApplicantPhone.Contains(query) ||
                             ja.CoverLetter.Contains(query) ||
                             ja.Status.Contains(query) ||
                             ja.Notes.Contains(query) ||
                             (ja.JobListing != null && ja.JobListing.Title.Contains(query))))
                .Take(2)
                .Select(ja => new
                {
                    type = "application",
                    title = ja.ApplicantName,
                    subtitle = ja.JobListing != null ? ja.JobListing.Title : "Job Application",
                    url = Url.Action("ApplicationDetails", "Jobs", new { area = "Admin", id = ja.Id }),
                    id = ja.Id,
                    description = $"{ja.ApplicantEmail} • {ja.Status}"
                })
                .ToListAsync();

            results.AddRange(jobApplications);

            // Search Contact Forms - ALL fields
            var contactForms = await _contactFormRepository.GetAll()
                .Where(cf => !cf.IsDeleted &&
                            (cf.Name.Contains(query) ||
                             cf.Email.Contains(query) ||
                             cf.Phone.Contains(query) ||
                             cf.Subject.Contains(query) ||
                             cf.Message.Contains(query) ||
                             cf.Status.Contains(query)))
                .Take(2)
                .Select(cf => new
                {
                    type = "contact",
                    title = cf.Name,
                    subtitle = cf.Subject,
                    url = Url.Action("Details", "ContactForms", new { area = "Admin", id = cf.Id }),
                    id = cf.Id,
                    description = $"{cf.Email} • {cf.Status}"
                })
                .ToListAsync();

            results.AddRange(contactForms);

            // Search Payments - ALL fields
            var payments = await _paymentRepository.GetAll()
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Client)
                .Where(p => !p.IsDeleted &&
                           (p.TransactionId.Contains(query) ||
                            p.PaymentMethod.Contains(query) ||
                            p.Status.Contains(query) ||
                            p.Notes.Contains(query) ||
                            p.Amount.ToString().Contains(query) ||
                            (p.Invoice != null && (p.Invoice.InvoiceNumber.Contains(query) ||
                                                   (p.Invoice.Client != null && p.Invoice.Client.ContactName.Contains(query))))))
                .Take(2)
                .Select(p => new
                {
                    type = "payment",
                    title = p.TransactionId,
                    subtitle = p.Invoice != null ? p.Invoice.InvoiceNumber : "Payment",
                    url = Url.Action("Details", "Payments", new { area = "Admin", id = p.Id }),
                    id = p.Id,
                    description = $"${p.Amount:F2} • {p.PaymentMethod} • {p.Status}"
                })
                .ToListAsync();

            results.AddRange(payments);

            // Search Messages - ALL fields
            var messages = await _messageRepository.GetAll()
                .Include(m => m.Project)
                .Where(m => !m.IsDeleted &&
                           (m.Content.Contains(query) ||
                            m.SenderName.Contains(query) ||
                            m.SenderRole.Contains(query) ||
                            m.SenderId.Contains(query) ||
                            (m.Project != null && m.Project.Name.Contains(query))))
                .Take(2)
                .Select(m => new
                {
                    type = "message",
                    title = m.SenderName,
                    subtitle = m.Project != null ? m.Project.Name : "Message",
                    url = Url.Action("Messages", "Projects", new { area = "Admin", id = m.ProjectId }),
                    id = m.Id,
                    description = m.Content.Length > 80 ? m.Content.Substring(0, 80) + "..." : m.Content
                })
                .ToListAsync();

            results.AddRange(messages);

            // Search Project Documents - ALL fields
            var projectDocuments = await _projectDocumentRepository.GetAll()
                .Include(pd => pd.Project)
                .Where(pd => !pd.IsDeleted &&
                            (pd.FileName.Contains(query) ||
                             pd.FileType.Contains(query) ||
                             pd.UploadedByName.Contains(query) ||
                             (pd.Project != null && pd.Project.Name.Contains(query))))
                .Take(2)
                .Select(pd => new
                {
                    type = "document",
                    title = pd.FileName,
                    subtitle = pd.Project != null ? pd.Project.Name : "Document",
                    url = Url.Action("Documents", "Projects", new { area = "Admin", id = pd.ProjectId }),
                    id = pd.Id,
                    description = $"{pd.FileType} • {pd.UploadedByName}"
                })
                .ToListAsync();

            results.AddRange(projectDocuments);

            // Search Site Settings - ALL fields
            var siteSettings = await _siteSettingRepository.GetAll()
                .Where(ss => !ss.IsDeleted &&
                            (ss.Key.Contains(query) ||
                             ss.Value.Contains(query) ||
                             ss.Group.Contains(query) ||
                             ss.Description.Contains(query) ||
                             ss.Icon.Contains(query)))
                .Take(2)
                .Select(ss => new
                {
                    type = "setting",
                    title = ss.Key,
                    subtitle = ss.Group,
                    url = Url.Action("Index", "Settings", new { area = "Admin" }),
                    id = ss.Id,
                    description = ss.Description.Length > 80 ? ss.Description.Substring(0, 80) + "..." : ss.Description
                })
                .ToListAsync();

            results.AddRange(siteSettings);

            // Search Feedback - ALL fields
            var feedbacks = await _feedbackRepository.GetAll()
                .Include(f => f.Client)
                .Where(f => !f.IsDeleted &&
                           (f.Subject.Contains(query) ||
                            f.Message.Contains(query) ||
                            f.FeedbackType.Contains(query) ||
                            f.Priority.Contains(query) ||
                            f.Status.Contains(query) ||
                            f.AdminResponse.Contains(query) ||
                            f.AdminName.Contains(query) ||
                            f.ClientName.Contains(query) ||
                            f.ClientEmail.Contains(query) ||
                            (f.Client != null && f.Client.ContactName.Contains(query))))
                .Take(2)
                .Select(f => new
                {
                    type = "feedback",
                    title = f.Subject,
                    subtitle = f.Client != null ? f.Client.ContactName : f.ClientName,
                    url = Url.Action("Index", "ContactForms", new { area = "Admin" }),
                    id = f.Id,
                    description = $"{f.FeedbackType} • {f.Priority} • {f.Status}"
                })
                .ToListAsync();

            results.AddRange(feedbacks);

            // Search Legal Pages - ALL fields
            var legalPages = await _legalPageRepository.GetAll()
                .Where(lp => !lp.IsDeleted &&
                            (lp.Title.Contains(query) ||
                             lp.Slug.Contains(query) ||
                             lp.Content.Contains(query) ||
                             lp.MetaDescription.Contains(query) ||
                             lp.ModifiedBy.Contains(query)))
                .Take(2)
                .Select(lp => new
                {
                    type = "legal",
                    title = lp.Title,
                    subtitle = "Legal Page",
                    url = Url.Action("Details", "LegalPages", new { area = "Admin", id = lp.Id }),
                    id = lp.Id,
                    description = lp.MetaDescription != null && lp.MetaDescription.Length > 80 ? lp.MetaDescription.Substring(0, 80) + "..." : lp.MetaDescription
                })
                .ToListAsync();

            results.AddRange(legalPages);

            // Search About Pages - ALL fields
            var aboutPages = await _aboutPageRepository.GetAll()
                .Where(ap => !ap.IsDeleted &&
                            (ap.Title.Contains(query) ||
                             ap.HeroTitle.Contains(query) ||
                             ap.HeroSubtitle.Contains(query) ||
                             ap.StoryTitle.Contains(query) ||
                             ap.StorySubtitle.Contains(query) ||
                             ap.StoryContent.Contains(query) ||
                             ap.MissionTitle.Contains(query) ||
                             ap.MissionContent.Contains(query) ||
                             ap.VisionTitle.Contains(query) ||
                             ap.VisionContent.Contains(query) ||
                             ap.ValuesTitle.Contains(query) ||
                             ap.ValuesContent.Contains(query) ||
                             ap.CtaTitle.Contains(query) ||
                             ap.CtaSubtitle.Contains(query) ||
                             ap.MetaDescription.Contains(query) ||
                             ap.MetaKeywords.Contains(query)))
                .Take(2)
                .Select(ap => new
                {
                    type = "about",
                    title = ap.Title,
                    subtitle = "About Page",
                    url = Url.Action("Details", "AboutPages", new { area = "Admin", id = ap.Id }),
                    id = ap.Id,
                    description = ap.HeroSubtitle != null && ap.HeroSubtitle.Length > 80 ? ap.HeroSubtitle.Substring(0, 80) + "..." : ap.HeroSubtitle
                })
                .ToListAsync();

            results.AddRange(aboutPages);

            // Search Hero Sections - ALL fields
            var heroSections = await _heroSectionRepository.GetAll()
                .Where(hs => !hs.IsDeleted &&
                            (hs.Title.Contains(query) ||
                             hs.PageName.Contains(query) ||
                             hs.MainTitle.Contains(query) ||
                             hs.MainSubtitle.Contains(query) ||
                             hs.MainDescription.Contains(query) ||
                             hs.PrimaryButtonText.Contains(query) ||
                             hs.SecondaryButtonText.Contains(query) ||
                             hs.MetaDescription.Contains(query) ||
                             hs.MetaKeywords.Contains(query) ||
                             hs.ModifiedBy.Contains(query)))
                .Take(2)
                .Select(hs => new
                {
                    type = "hero",
                    title = hs.Title,
                    subtitle = hs.PageName,
                    url = Url.Action("Details", "HeroSections", new { area = "Admin", id = hs.Id }),
                    id = hs.Id,
                    description = hs.MainSubtitle != null && hs.MainSubtitle.Length > 80 ? hs.MainSubtitle.Substring(0, 80) + "..." : hs.MainSubtitle
                })
                .ToListAsync();

            results.AddRange(heroSections);

            // Search Chatbot Intents - ALL fields
            var chatbotIntents = await _chatbotIntentRepository.GetAll()
                .Where(ci => !ci.IsDeleted &&
                            (ci.Name.Contains(query) ||
                             ci.DisplayName.Contains(query) ||
                             ci.Description.Contains(query) ||
                             ci.IconClass.Contains(query)))
                .Take(2)
                .Select(ci => new
                {
                    type = "chatbot-intent",
                    title = ci.DisplayName,
                    subtitle = "Chatbot Intent",
                    url = Url.Action("Intents", "Chatbot", new { area = "Admin" }),
                    id = ci.Id,
                    description = ci.Description != null && ci.Description.Length > 80 ? ci.Description.Substring(0, 80) + "..." : ci.Description
                })
                .ToListAsync();

            results.AddRange(chatbotIntents);

            // Search Chatbot Responses - ALL fields
            var chatbotResponses = await _chatbotResponseRepository.GetAll()
                .Include(cr => cr.ChatbotIntent)
                .Where(cr => !cr.IsDeleted &&
                            (cr.Content.Contains(query) ||
                             cr.ResponseType.Contains(query) ||
                             cr.Conditions.Contains(query) ||
                             cr.TemplateVariables.Contains(query) ||
                             (cr.ChatbotIntent != null && cr.ChatbotIntent.DisplayName.Contains(query))))
                .Take(2)
                .Select(cr => new
                {
                    type = "chatbot-response",
                    title = cr.ChatbotIntent != null ? cr.ChatbotIntent.DisplayName : "Chatbot Response",
                    subtitle = cr.ResponseType,
                    url = Url.Action("Responses", "Chatbot", new { area = "Admin" }),
                    id = cr.Id,
                    description = cr.Content != null && cr.Content.Length > 80 ? cr.Content.Substring(0, 80) + "..." : cr.Content
                })
                .ToListAsync();

            results.AddRange(chatbotResponses);

            // Search Chatbot Keywords - ALL fields
            var chatbotKeywords = await _chatbotKeywordRepository.GetAll()
                .Include(ck => ck.ChatbotIntent)
                .Where(ck => !ck.IsDeleted &&
                            (ck.Keyword.Contains(query) ||
                             ck.Synonyms.Contains(query) ||
                             ck.MatchType.Contains(query) ||
                             (ck.ChatbotIntent != null && ck.ChatbotIntent.DisplayName.Contains(query))))
                .Take(2)
                .Select(ck => new
                {
                    type = "chatbot-keyword",
                    title = ck.Keyword,
                    subtitle = ck.ChatbotIntent != null ? ck.ChatbotIntent.DisplayName : "Chatbot Keyword",
                    url = Url.Action("Keywords", "Chatbot", new { area = "Admin" }),
                    id = ck.Id,
                    description = $"{ck.MatchType} • Weight: {ck.Weight}"
                })
                .ToListAsync();

            results.AddRange(chatbotKeywords);

            return Json(new { results = results.Take(25) });
        }
        catch (Exception ex)
        {
            return Json(new { error = "Search failed", message = ex.Message });
        }
    }
}
