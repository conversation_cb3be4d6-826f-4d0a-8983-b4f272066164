@model IEnumerable<Technoloway.Core.Entities.ServiceFeature>
@{
    ViewData["Title"] = $"Service Features - {ViewBag.Service.Name}";
    var service = ViewBag.Service as Technoloway.Core.Entities.Service;
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>Service Features</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Services")">Services</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("Details", "Services", new { id = service?.Id })">@service?.Name</a></li>
                <li class="breadcrumb-item active">Features</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="@Url.Action("CreateFeature", new { serviceId = service?.Id })" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Feature
        </a>
        <a href="@Url.Action("Details", "Services", new { id = service?.Id })" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Service
        </a>
    </div>
</div>

<!-- Service Info Card -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="card-title mb-1">@service?.Name</h5>
                <p class="text-muted mb-0">@service?.Description</p>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge bg-primary fs-6">Base Price: $@service?.BasePrice.ToString("N0")</span>
            </div>
        </div>
    </div>
</div>

@if (Model.Any())
{
    <!-- Features Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Features (@Model.Count())</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Feature</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Pricing Type</th>
                            <th>Status</th>
                            <th>Order</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var feature in Model.OrderBy(f => f.DisplayOrder).ThenBy(f => f.Name))
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(feature.IconClass))
                                        {
                                            <i class="@feature.IconClass text-primary me-2"></i>
                                        }
                                        <div>
                                            <div class="fw-medium">@feature.Name</div>
                                            @if (!string.IsNullOrEmpty(feature.Description))
                                            {
                                                <small class="text-muted">@feature.Description</small>
                                            }
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-@(feature.FeatureType switch {
                                        "Included" => "success",
                                        "Optional" => "primary",
                                        "Premium" => "warning",
                                        _ => "secondary"
                                    })">
                                        @feature.FeatureType
                                    </span>
                                    @if (feature.IsRequired)
                                    {
                                        <span class="badge bg-danger ms-1">Required</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(feature.Category))
                                    {
                                        <span class="badge bg-light text-dark">@feature.Category</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (feature.AdditionalPrice > 0)
                                    {
                                        <span class="fw-medium text-success">+$@feature.AdditionalPrice.ToString("N0")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Free</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-@(feature.PricingType switch {
                                        "OneTime" => "info",
                                        "Monthly" => "warning",
                                        "PerUnit" => "primary",
                                        _ => "secondary"
                                    })">
                                        @feature.PricingType
                                    </span>
                                </td>
                                <td>
                                    @if (feature.IsActive)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Inactive</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">@feature.DisplayOrder</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="@Url.Action("EditFeature", new { id = feature.Id })" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="confirmDelete(@feature.Id, '@feature.Name')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
else
{
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
            <h5>No Features Added Yet</h5>
            <p class="text-muted mb-4">Start by adding features to enhance your service offering.</p>
            <a href="@Url.Action("CreateFeature", new { serviceId = service?.Id })" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Feature
            </a>
        </div>
    </div>
}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the feature "<span id="featureName"></span>"?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete Feature</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(featureId, featureName) {
            document.getElementById('featureName').textContent = featureName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteFeature")/' + featureId;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
}
