using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Service : BaseEntity
{
    [Required(ErrorMessage = "Service name is required")]
    [StringLength(100, ErrorMessage = "Service name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Service description is required")]
    [StringLength(2000, ErrorMessage = "Service description cannot exceed 2000 characters")]
    public string Description { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "Short description cannot exceed 1000 characters")]
    public string ShortDescription { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
    public string IconClass { get; set; } = string.Empty;

    [Range(0, double.MaxValue, ErrorMessage = "Base price must be greater than or equal to 0")]
    public decimal BasePrice { get; set; }

    [Obsolete("Use BasePrice instead. This property is kept for backward compatibility.")]
    public decimal Price
    {
        get => BasePrice;
        set => BasePrice = value;
    }

    [StringLength(20, ErrorMessage = "Pricing model cannot exceed 20 characters")]
    public string PricingModel { get; set; } = "Fixed"; // Fixed, Tiered, PerFeature, Custom

    [StringLength(500, ErrorMessage = "Features list cannot exceed 500 characters")]
    public string Features { get; set; } = string.Empty; // JSON string of features

    [StringLength(200, ErrorMessage = "Image URL cannot exceed 200 characters")]
    public string? ImageUrl { get; set; }

    [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
    public string Tags { get; set; } = string.Empty; // Comma-separated tags

    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; } = false;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    [Range(1, 365, ErrorMessage = "Estimated delivery days must be between 1 and 365")]
    public int EstimatedDeliveryDays { get; set; } = 30;

    // Navigation properties
    public ICollection<Project> Projects { get; set; } = new List<Project>();
    public ICollection<ServiceFeature> ServiceFeatures { get; set; } = new List<ServiceFeature>();
    public ICollection<ServicePricingTier> PricingTiers { get; set; } = new List<ServicePricingTier>();
    public ICollection<QuoteItem> QuoteItems { get; set; } = new List<QuoteItem>();
}
