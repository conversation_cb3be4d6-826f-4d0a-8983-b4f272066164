@model IEnumerable<Technoloway.Core.Entities.Quote>
@{
    ViewData["Title"] = "Quotes Management";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>Quotes Management</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Dashboard</a></li>
                <li class="breadcrumb-item active">Quotes</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="@Url.Action("Analytics")" class="btn btn-outline-primary">
            <i class="fas fa-chart-bar me-2"></i>Analytics
        </a>
    </div>
</div>

<!-- Status Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="card-title mb-0">Filter by Status</h5>
            </div>
            <div class="col-md-4">
                <div class="btn-group w-100" role="group">
                    <a href="@Url.Action("Quotes")" class="btn @(string.IsNullOrEmpty(ViewBag.SelectedStatus) ? "btn-primary" : "btn-outline-primary") btn-sm">
                        All (@(ViewBag.StatusCounts?.Values.Sum() ?? 0))
                    </a>
                    @if (ViewBag.StatusCounts != null)
                    {
                        @foreach (var status in ViewBag.StatusCounts)
                        {
                            <a href="@Url.Action("Quotes", new { status = status.Key })" 
                               class="btn @(ViewBag.SelectedStatus == status.Key ? "btn-primary" : "btn-outline-primary") btn-sm">
                                @status.Key (@status.Value)
                            </a>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@if (Model.Any())
{
    <!-- Quotes Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Quotes (@Model.Count())</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Quote #</th>
                            <th>Client</th>
                            <th>Total Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Expires</th>
                            <th>Delivery</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var quote in Model)
                        {
                            <tr>
                                <td>
                                    <div class="fw-medium">@quote.QuoteNumber</div>
                                    @if (!string.IsNullOrEmpty(quote.CompanyName))
                                    {
                                        <small class="text-muted">@quote.CompanyName</small>
                                    }
                                </td>
                                <td>
                                    <div>@quote.ClientName</div>
                                    <small class="text-muted">@quote.ClientEmail</small>
                                </td>
                                <td>
                                    <div class="fw-bold text-success">$@quote.TotalAmount.ToString("N2")</div>
                                    @if (quote.DiscountAmount > 0)
                                    {
                                        <small class="text-muted">Discount: $@quote.DiscountAmount.ToString("N2")</small>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-@(quote.Status switch {
                                        "Draft" => "secondary",
                                        "Sent" => "primary",
                                        "Accepted" => "success",
                                        "Rejected" => "danger",
                                        "Expired" => "warning",
                                        _ => "secondary"
                                    })">
                                        @quote.Status
                                    </span>
                                    @if (quote.Status == "Sent" && quote.ExpiresAt.HasValue && quote.ExpiresAt < DateTime.UtcNow)
                                    {
                                        <span class="badge bg-warning ms-1">Expired</span>
                                    }
                                </td>
                                <td>
                                    <div>@quote.CreatedAt.ToString("MMM dd, yyyy")</div>
                                    <small class="text-muted">@quote.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td>
                                    @if (quote.ExpiresAt.HasValue)
                                    {
                                        <div>@quote.ExpiresAt.Value.ToString("MMM dd, yyyy")</div>
                                        @if (quote.ExpiresAt < DateTime.UtcNow && quote.Status == "Sent")
                                        {
                                            <small class="text-danger">Expired</small>
                                        }
                                        else if (quote.ExpiresAt < DateTime.UtcNow.AddDays(3) && quote.Status == "Sent")
                                        {
                                            <small class="text-warning">Expires soon</small>
                                        }
                                        else
                                        {
                                            <small class="text-muted">@((quote.ExpiresAt.Value - DateTime.UtcNow).Days) days left</small>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@quote.EstimatedDeliveryDays days</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="@Url.Action("QuoteDetails", new { id = quote.Id })" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (quote.Status == "Draft")
                                        {
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="updateStatus(@quote.Id, 'Sent')" title="Send Quote">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        }
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                                    data-bs-toggle="dropdown" title="More Actions">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="@Url.Action("QuoteDetails", new { id = quote.Id })">
                                                    <i class="fas fa-eye me-2"></i>View Details
                                                </a></li>
                                                @if (quote.Status != "Accepted")
                                                {
                                                    <li><button class="dropdown-item" onclick="updateStatus(@quote.Id, 'Accepted')">
                                                        <i class="fas fa-check me-2"></i>Mark as Accepted
                                                    </button></li>
                                                }
                                                @if (quote.Status != "Rejected")
                                                {
                                                    <li><button class="dropdown-item" onclick="updateStatus(@quote.Id, 'Rejected')">
                                                        <i class="fas fa-times me-2"></i>Mark as Rejected
                                                    </button></li>
                                                }
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-primary" href="/Pricing/Quote/@quote.Id" target="_blank">
                                                    <i class="fas fa-external-link-alt me-2"></i>View Public Quote
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
else
{
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
            <h5>No Quotes Found</h5>
            <p class="text-muted mb-4">
                @if (!string.IsNullOrEmpty(ViewBag.SelectedStatus))
                {
                    <span>No quotes with status "@ViewBag.SelectedStatus" found.</span>
                }
                else
                {
                    <span>No quotes have been created yet. Quotes will appear here when customers request them.</span>
                }
            </p>
            @if (string.IsNullOrEmpty(ViewBag.SelectedStatus))
            {
                <a href="/pricing" class="btn btn-primary" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>View Public Pricing Page
                </a>
            }
            else
            {
                <a href="@Url.Action("Quotes")" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>View All Quotes
                </a>
            }
        </div>
    </div>
}

@section Scripts {
    <script>
        async function updateStatus(quoteId, status) {
            if (!confirm(`Are you sure you want to mark this quote as ${status}?`)) {
                return;
            }

            try {
                const response = await fetch(`@Url.Action("UpdateQuoteStatus")`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: `id=${quoteId}&status=${status}`
                });

                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error updating quote status. Please try again.');
                }
            } catch (error) {
                console.error('Error updating quote status:', error);
                alert('Error updating quote status. Please try again.');
            }
        }

        // Auto-refresh every 30 seconds for real-time updates
        setInterval(() => {
            // Only refresh if no modals are open
            if (!document.querySelector('.modal.show')) {
                location.reload();
            }
        }, 30000);
    </script>
}
