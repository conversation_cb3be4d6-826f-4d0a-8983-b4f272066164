using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class QuoteItemFeature : BaseEntity
{
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; } = 1;
    
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Total price must be greater than or equal to 0")]
    public decimal TotalPrice { get; set; }
    
    [StringLength(500, ErrorMessage = "Configuration cannot exceed 500 characters")]
    public string Configuration { get; set; } = string.Empty; // JSON string for feature-specific config
    
    // Foreign keys
    [Required(ErrorMessage = "Quote item is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid quote item")]
    public int QuoteItemId { get; set; }
    
    [Required(ErrorMessage = "Service feature is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service feature")]
    public int ServiceFeatureId { get; set; }
    
    // Navigation properties
    public QuoteItem QuoteItem { get; set; } = null!;
    public ServiceFeature ServiceFeature { get; set; } = null!;
}
