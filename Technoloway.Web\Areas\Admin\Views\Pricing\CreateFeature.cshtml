@model Technoloway.Core.Entities.ServiceFeature
@{
    ViewData["Title"] = "Create Service Feature";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
    var service = ViewBag.Service as Technoloway.Core.Entities.Service;
}

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("Index")">Pricing Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("ServiceFeatures", new { serviceId = service.Id })">@service.Name - Features</a>
                    </li>
                    <li class="breadcrumb-item active">Create Feature</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus me-2"></i>Create Service Feature
            </h1>
            <p class="text-muted mb-0">Add a new feature for <strong>@service.Name</strong></p>
        </div>
        <div>
            <a href="@Url.Action("ServiceFeatures", new { serviceId = service.Id })" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Features
            </a>
        </div>
    </div>

    <!-- Service Info -->
    <div class="card mb-4 border-left-primary">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="text-primary mb-1">@service.Name</h6>
                    <p class="text-muted mb-0 small">@service.ShortDescription</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-info">Base: @service.BasePrice.ToString("C")</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Feature Details
                    </h6>
                </div>
                <div class="card-body">
                    <form asp-action="CreateFeature" method="post">
                        <input asp-for="ServiceId" type="hidden" />
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">Feature Name *</label>
                                    <input asp-for="Name" class="form-control" placeholder="e.g., Responsive Design" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="IconClass" class="form-label">Icon Class</label>
                                    <input asp-for="IconClass" class="form-control" placeholder="e.g., fas fa-mobile-alt" />
                                    <div class="form-text">FontAwesome icon class</div>
                                    <span asp-validation-for="IconClass" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">Description</label>
                            <textarea asp-for="Description" class="form-control" rows="3" 
                                      placeholder="Brief description of this feature"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="FeatureType" class="form-label">Feature Type *</label>
                                    <select asp-for="FeatureType" class="form-select">
                                        <option value="">Select Type</option>
                                        <option value="Included">Included</option>
                                        <option value="Optional">Optional</option>
                                        <option value="Premium">Premium</option>
                                    </select>
                                    <span asp-validation-for="FeatureType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Category" class="form-label">Category</label>
                                    <input asp-for="Category" class="form-control" placeholder="e.g., Design, Development" />
                                    <span asp-validation-for="Category" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="DisplayOrder" class="form-label">Display Order</label>
                                    <input asp-for="DisplayOrder" class="form-control" type="number" min="1" value="1" />
                                    <div class="form-text">Lower numbers appear first</div>
                                    <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="AdditionalPrice" class="form-label">Additional Price</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input asp-for="AdditionalPrice" class="form-control" type="number" step="0.01" min="0" value="0" />
                                    </div>
                                    <div class="form-text">Leave 0 for included features</div>
                                    <span asp-validation-for="AdditionalPrice" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PricingType" class="form-label">Pricing Type</label>
                                    <select asp-for="PricingType" class="form-select">
                                        <option value="OneTime">One Time</option>
                                        <option value="Monthly">Monthly</option>
                                        <option value="PerUnit">Per Unit</option>
                                    </select>
                                    <span asp-validation-for="PricingType" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Options</label>
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                        <label asp-for="IsActive" class="form-check-label">Active</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="IsRequired" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsRequired" class="form-check-label">Required Feature</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="@Url.Action("ServiceFeatures", new { serviceId = service.Id })" 
                               class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Create Feature
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white text-center py-2">
                    <small><i class="fas fa-eye me-1"></i>Preview</small>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <i id="previewIcon" class="fas fa-puzzle-piece fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1" id="previewName">Feature Name</h6>
                            <p class="text-muted small mb-0" id="previewDescription">Feature description will appear here</p>
                        </div>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <span id="previewType" class="badge bg-secondary">Type</span>
                                <div class="small text-muted mt-1">Feature Type</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <span id="previewPrice" class="text-success fw-bold">Free</span>
                            <div class="small text-muted mt-1" id="previewPricingType">Pricing</div>
                        </div>
                    </div>

                    <div class="text-center">
                        <span id="previewCategory" class="badge bg-light text-dark">Category</span>
                        <span id="previewOrder" class="badge bg-info ms-1">#1</span>
                    </div>

                    <div class="mt-3 text-center">
                        <span id="previewStatus" class="badge bg-success">Active</span>
                        <span id="previewRequired" class="badge bg-danger ms-1" style="display: none;">Required</span>
                    </div>
                </div>
            </div>

            <!-- Feature Type Guide -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">Feature Types</h6>
                    <div class="small">
                        <div class="mb-2">
                            <span class="badge bg-success me-2">Included</span>
                            <span class="text-muted">Part of base service</span>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-primary me-2">Optional</span>
                            <span class="text-muted">Customer can choose to add</span>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-warning me-2">Premium</span>
                            <span class="text-muted">High-value add-on</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.querySelector('[name="Name"]').value || 'Feature Name';
            const description = document.querySelector('[name="Description"]').value || 'Feature description will appear here';
            const featureType = document.querySelector('[name="FeatureType"]').value || 'Type';
            const category = document.querySelector('[name="Category"]').value || 'Category';
            const additionalPrice = parseFloat(document.querySelector('[name="AdditionalPrice"]').value) || 0;
            const pricingType = document.querySelector('[name="PricingType"]').value || 'OneTime';
            const displayOrder = document.querySelector('[name="DisplayOrder"]').value || 1;
            const iconClass = document.querySelector('[name="IconClass"]').value || 'fas fa-puzzle-piece';
            const isActive = document.querySelector('[name="IsActive"]').checked;
            const isRequired = document.querySelector('[name="IsRequired"]').checked;

            document.getElementById('previewName').textContent = name;
            document.getElementById('previewDescription').textContent = description;
            document.getElementById('previewCategory').textContent = category;
            document.getElementById('previewOrder').textContent = '#' + displayOrder;
            document.getElementById('previewIcon').className = iconClass + ' fa-2x text-primary';

            // Update feature type badge
            const typeElement = document.getElementById('previewType');
            typeElement.textContent = featureType;
            typeElement.className = 'badge bg-' + (featureType === 'Included' ? 'success' : featureType === 'Optional' ? 'primary' : featureType === 'Premium' ? 'warning' : 'secondary');

            // Update price
            const priceElement = document.getElementById('previewPrice');
            if (additionalPrice > 0) {
                priceElement.textContent = '+$' + additionalPrice.toFixed(2);
                priceElement.className = 'text-primary fw-bold';
            } else {
                priceElement.textContent = 'Free';
                priceElement.className = 'text-success fw-bold';
            }

            document.getElementById('previewPricingType').textContent = pricingType;

            // Update status
            const statusElement = document.getElementById('previewStatus');
            statusElement.textContent = isActive ? 'Active' : 'Inactive';
            statusElement.className = 'badge bg-' + (isActive ? 'success' : 'secondary');

            // Update required badge
            const requiredElement = document.getElementById('previewRequired');
            requiredElement.style.display = isRequired ? 'inline' : 'none';
        }

        // Add event listeners for live preview
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = ['Name', 'Description', 'FeatureType', 'Category', 'AdditionalPrice', 'PricingType', 'DisplayOrder', 'IconClass'];
            inputs.forEach(inputName => {
                const element = document.querySelector(`[name="${inputName}"]`);
                if (element) {
                    element.addEventListener('input', updatePreview);
                    element.addEventListener('change', updatePreview);
                }
            });

            const checkboxes = ['IsActive', 'IsRequired'];
            checkboxes.forEach(checkboxName => {
                const element = document.querySelector(`[name="${checkboxName}"]`);
                if (element) {
                    element.addEventListener('change', updatePreview);
                }
            });
            
            // Initial preview update
            updatePreview();
        });
    </script>
}
