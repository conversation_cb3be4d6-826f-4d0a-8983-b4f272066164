using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class ServiceFeature : BaseEntity
{
    [Required(ErrorMessage = "Feature name is required")]
    [StringLength(100, ErrorMessage = "Feature name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "Feature description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;
    
    [StringLength(20, ErrorMessage = "Feature type cannot exceed 20 characters")]
    public string FeatureType { get; set; } = "Included"; // Included, Optional, Premium
    
    [Range(0, double.MaxValue, ErrorMessage = "Additional price must be greater than or equal to 0")]
    public decimal AdditionalPrice { get; set; } = 0;
    
    [StringLength(20, ErrorMessage = "Pricing type cannot exceed 20 characters")]
    public string PricingType { get; set; } = "OneTime"; // OneTime, Monthly, PerUnit
    
    [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
    public string IconClass { get; set; } = string.Empty;
    
    public bool IsActive { get; set; } = true;
    public bool IsRequired { get; set; } = false;
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    [StringLength(100, ErrorMessage = "Category cannot exceed 100 characters")]
    public string Category { get; set; } = string.Empty;
    
    // Foreign key
    [Required(ErrorMessage = "Service is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service")]
    public int ServiceId { get; set; }
    
    // Navigation properties
    public Service Service { get; set; } = null!;
    public ICollection<QuoteItemFeature> QuoteItemFeatures { get; set; } = new List<QuoteItemFeature>();
}
