@using Technoloway.Core.Entities
@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-text">
                <h1 class="dashboard-title">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard Overview
                </h1>
                <p class="dashboard-subtitle">
                    Welcome back! Here's what's happening with your business today.
                    <span class="current-time" id="currentTime"></span>
                </p>
            </div>
            <div class="header-actions">
                <button class="action-btn secondary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-download"></i>
                    <span>Export Report</span>
                </button>
                <div class="dropdown">
                    <button class="action-btn primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-plus"></i>
                        <span>Quick Add</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Projects" asp-action="Create">
                            <i class="fas fa-project-diagram me-2"></i>New Project
                        </a></li>
                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Clients" asp-action="Create">
                            <i class="fas fa-user-tie me-2"></i>New Client
                        </a></li>
                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Invoices" asp-action="Create">
                            <i class="fas fa-file-invoice me-2"></i>New Invoice
                        </a></li>
                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Blog" asp-action="Create">
                            <i class="fas fa-blog me-2"></i>New Blog Post
                        </a></li>
                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Pricing" asp-action="Quotes">
                            <i class="fas fa-calculator me-2"></i>View Quotes
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="kpi-grid">
        <!-- Revenue Card -->
        <a href="@Url.Action("Index", "Invoices", new { area = "Admin" })" class="kpi-card-link">
            <div class="kpi-card revenue-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>@(ViewBag.RevenueGrowth?.ToString("F1") ?? "0")%</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">$@(ViewBag.TotalRevenue?.ToString("N0") ?? "0")</h3>
                    <p class="kpi-label">Total Revenue</p>
                    <div class="kpi-breakdown">
                        <div class="breakdown-item">
                            <span class="breakdown-label">Paid</span>
                            <span class="breakdown-value">$@(ViewBag.PaidRevenue?.ToString("N0") ?? "0")</span>
                        </div>
                        <div class="breakdown-item">
                            <span class="breakdown-label">Pending</span>
                            <span class="breakdown-value">$@(ViewBag.PendingRevenue?.ToString("N0") ?? "0")</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-chart">
                    <canvas id="revenueSparkline" width="100" height="30"></canvas>
                </div>
                <div class="kpi-overlay">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Invoices</span>
                </div>
            </div>
        </a>

        <!-- Projects Card -->
        <a href="@Url.Action("Index", "Projects", new { area = "Admin" })" class="kpi-card-link">
            <div class="kpi-card projects-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="kpi-badge">
                        <span>@ViewBag.ActiveProjects Active</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@ViewBag.ProjectCount</h3>
                    <p class="kpi-label">Total Projects</p>
                    <div class="progress-ring">
                        <div class="progress-circle" data-percentage="@(ViewBag.ProjectCount > 0 ? (ViewBag.CompletedProjects * 100 / ViewBag.ProjectCount) : 0)">
                            <span class="progress-text">@(ViewBag.ProjectCount > 0 ? (ViewBag.CompletedProjects * 100 / ViewBag.ProjectCount) : 0)%</span>
                        </div>
                        <p class="progress-label">Completed</p>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Projects</span>
                </div>
            </div>
        </a>

        <!-- Clients Card -->
        <a href="@Url.Action("Index", "Clients", new { area = "Admin" })" class="kpi-card-link">
            <div class="kpi-card clients-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="kpi-actions">
                        <button class="kpi-action-btn" title="View All Clients">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@ViewBag.ClientCount</h3>
                    <p class="kpi-label">Active Clients</p>
                    <div class="client-stats">
                        <div class="stat-item">
                            <i class="fas fa-user-plus"></i>
                            <span>5 New this month</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-handshake"></i>
                            <span>@ViewBag.ProjectCount Projects</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Clients</span>
                </div>
            </div>
        </a>

        <!-- Notifications Card -->
        <a href="@Url.Action("Index", "ContactForms", new { area = "Admin" })" class="kpi-card-link">
            <div class="kpi-card notifications-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="kpi-badge urgent">
                        <span>@ViewBag.UnreadContactCount Unread</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@ViewBag.PendingInvoiceCount</h3>
                    <p class="kpi-label">Pending Invoices</p>
                    <div class="notification-list">
                        <div class="notification-item">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <span>@ViewBag.UnreadContactCount Contact forms</span>
                        </div>
                        <div class="notification-item">
                            <i class="fas fa-briefcase text-info"></i>
                            <span>@ViewBag.JobApplicationCount Job applications</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Notifications</span>
                </div>
            </div>
        </a>

        <!-- Pricing & Quotes Card -->
        <a href="@Url.Action("Quotes", "Pricing", new { area = "Admin" })" class="kpi-card-link">
            <div class="kpi-card pricing-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="kpi-badge">
                        <span>@(ViewBag.PendingQuotes ?? 0) Pending</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@(ViewBag.TotalQuotes ?? 0)</h3>
                    <p class="kpi-label">Total Quotes</p>
                    <div class="quote-stats">
                        <div class="stat-item">
                            <i class="fas fa-check-circle text-success"></i>
                            <span>@(ViewBag.AcceptedQuotes ?? 0) Accepted</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-dollar-sign text-primary"></i>
                            <span>$@((ViewBag.QuotesValue ?? 0m).ToString("N0")) Value</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Quotes</span>
                </div>
            </div>
        </a>
    </div>

    <!-- Analytics Dashboard -->
    <div class="analytics-grid">
        <!-- Revenue Analytics Chart -->
        <div class="analytics-card chart-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line me-2"></i>
                        Revenue Analytics
                    </h3>
                    <p class="card-subtitle">Monthly revenue trends over the last 6 months</p>
                </div>
                <div class="header-actions">
                    <div class="time-filter">
                        <button class="filter-btn active" data-period="6m">6M</button>
                        <button class="filter-btn" data-period="1y">1Y</button>
                        <button class="filter-btn" data-period="all">All</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Project Status Distribution -->
        <div class="analytics-card donut-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie me-2"></i>
                        Project Status
                    </h3>
                    <p class="card-subtitle">Current project distribution</p>
                </div>
            </div>
            <div class="card-body">
                <div class="donut-chart-container">
                    <canvas id="projectStatusChart"></canvas>
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span class="legend-color" style="background: #4f46e5;"></span>
                        <span class="legend-label">Completed (@ViewBag.CompletedProjects)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background: #10b981;"></span>
                        <span class="legend-label">Active (@ViewBag.ActiveProjects)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="analytics-card activity-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-clock me-2"></i>
                        Recent Activity
                    </h3>
                    <p class="card-subtitle">Latest updates and changes</p>
                </div>
                <div class="header-actions">
                    <button class="refresh-btn" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="activity-timeline">
                    @{
                        var recentProjects = ViewBag.RecentProjects as List<Project>;
                        var recentClients = ViewBag.RecentClients as List<Client>;
                    }

                    @if (recentProjects != null && recentProjects.Count > 0)
                    {
                        @foreach (var project in recentProjects.Take(3))
                        {
                            <div class="activity-item">
                                <div class="activity-icon project">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h4 class="activity-title">New project created</h4>
                                        <span class="activity-time">@project.CreatedAt.ToString("MMM dd, HH:mm")</span>
                                    </div>
                                    <p class="activity-description">
                                        <strong>@project.Name</strong> for @project.ClientName
                                    </p>
                                </div>
                            </div>
                        }
                    }

                    @if (recentClients != null && recentClients.Count > 0)
                    {
                        @foreach (var client in recentClients.Take(2))
                        {
                            <div class="activity-item">
                                <div class="activity-icon client">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h4 class="activity-title">New client registered</h4>
                                        <span class="activity-time">@client.CreatedAt.ToString("MMM dd, HH:mm")</span>
                                    </div>
                                    <p class="activity-description">
                                        <strong>@client.CompanyName</strong> joined the platform
                                    </p>
                                </div>
                            </div>
                        }
                    }
                </div>
                <div class="activity-footer">
                    <a href="#" class="view-all-link">
                        <i class="fas fa-arrow-right me-1"></i>
                        View all activity
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats Grid -->
        <div class="analytics-card stats-grid-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar me-2"></i>
                        Quick Stats
                    </h3>
                    <p class="card-subtitle">Key metrics at a glance</p>
                </div>
            </div>
            <div class="card-body">
                <div class="quick-stats-grid">
                    <a href="@Url.Action("Index", "Blog", new { area = "Admin" })" class="quick-stat-link">
                        <div class="quick-stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-blog"></i>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-number">@ViewBag.BlogPostCount</h4>
                                <p class="stat-label">Blog Posts</p>
                            </div>
                        </div>
                    </a>
                    <a href="@Url.Action("Index", "Testimonials", new { area = "Admin" })" class="quick-stat-link">
                        <div class="quick-stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-number">@ViewBag.TestimonialCount</h4>
                                <p class="stat-label">Testimonials</p>
                            </div>
                        </div>
                    </a>
                    <a href="@Url.Action("Index", "JobApplications", new { area = "Admin" })" class="quick-stat-link">
                        <div class="quick-stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-number">@ViewBag.JobApplicationCount</h4>
                                <p class="stat-label">Job Applications</p>
                            </div>
                        </div>
                    </a>
                    <a href="@Url.Action("Index", "ContactForms", new { area = "Admin" })" class="quick-stat-link">
                        <div class="quick-stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-number">@ViewBag.UnreadContactCount</h4>
                                <p class="stat-label">Unread Messages</p>
                            </div>
                        </div>
                    </a>
                    <a href="@Url.Action("Quotes", "Pricing", new { area = "Admin" })" class="quick-stat-link">
                        <div class="quick-stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-number">@(ViewBag.PendingQuotes ?? 0)</h4>
                                <p class="stat-label">Pending Quotes</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Data Tables -->
    <div class="data-tables-grid">
        <!-- Recent Projects Table -->
        <div class="data-table-card">
            <div class="table-header">
                <div class="header-content">
                    <h3 class="table-title">
                        <i class="fas fa-project-diagram me-2"></i>
                        Recent Projects
                    </h3>
                    <p class="table-subtitle">Latest project activities</p>
                </div>
                <div class="header-actions">
                    <a asp-area="Admin" asp-controller="Projects" asp-action="Index" class="view-all-btn">
                        <span>View All</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            <div class="table-container">
                @{
                    var projectsForTable = ViewBag.RecentProjects as List<Project>;
                }

                @if (projectsForTable != null && projectsForTable.Count > 0)
                {
                    <div class="modern-table">
                        @foreach (var project in projectsForTable.Take(5))
                        {
                            <div class="table-row">
                                <div class="row-content">
                                    <div class="row-icon">
                                        <i class="fas fa-project-diagram"></i>
                                    </div>
                                    <div class="row-details">
                                        <h4 class="row-title">
                                            <a asp-area="Admin" asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id">
                                                @project.Name
                                            </a>
                                        </h4>
                                        <p class="row-subtitle">@project.ClientName</p>
                                    </div>
                                </div>
                                <div class="row-meta">
                                    <span class="meta-badge">@project.CreatedAt.ToString("MMM dd")</span>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <i class="fas fa-project-diagram"></i>
                        <p>No recent projects found</p>
                    </div>
                }
            </div>
        </div>

        <!-- Recent Invoices Table -->
        <div class="data-table-card">
            <div class="table-header">
                <div class="header-content">
                    <h3 class="table-title">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        Recent Invoices
                    </h3>
                    <p class="table-subtitle">Latest billing activities</p>
                </div>
                <div class="header-actions">
                    <a asp-area="Admin" asp-controller="Invoices" asp-action="Index" class="view-all-btn">
                        <span>View All</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            <div class="table-container">
                @{
                    var invoicesForTable = ViewBag.RecentInvoices as List<Invoice>;
                }

                @if (invoicesForTable != null && invoicesForTable.Count > 0)
                {
                    <div class="modern-table">
                        @foreach (var invoice in invoicesForTable.Take(5))
                        {
                            <div class="table-row">
                                <div class="row-content">
                                    <div class="row-icon invoice">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div class="row-details">
                                        <h4 class="row-title">
                                            <a asp-area="Admin" asp-controller="Invoices" asp-action="Details" asp-route-id="@invoice.Id">
                                                @invoice.InvoiceNumber
                                            </a>
                                        </h4>
                                        <p class="row-subtitle">$@invoice.TotalAmount.ToString("N2")</p>
                                    </div>
                                </div>
                                <div class="row-meta">
                                    @if (invoice.Status == "Paid")
                                    {
                                        <span class="status-badge paid">Paid</span>
                                    }
                                    else if (invoice.Status == "Pending")
                                    {
                                        <span class="status-badge pending">Pending</span>
                                    }
                                    else if (invoice.Status == "Overdue")
                                    {
                                        <span class="status-badge overdue">Overdue</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge">@invoice.Status</span>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <p>No recent invoices found</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Chart data from server
        const revenueData = @Html.Raw(ViewBag.RevenueChartData ?? "[]");
        const projectStatusData = @Html.Raw(ViewBag.ProjectStatusData ?? "[]");

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            initializeCharts();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        });

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = ` • ${timeString}`;
            }
        }

        function initializeDashboard() {
            // Initialize progress circles
            const progressCircles = document.querySelectorAll('.progress-circle');
            progressCircles.forEach(circle => {
                const percentage = circle.getAttribute('data-percentage');
                animateProgressCircle(circle, percentage);
            });

            // Initialize KPI animations
            animateKPICards();
        }

        function animateProgressCircle(circle, percentage) {
            // Simple progress circle animation
            circle.style.setProperty('--progress', percentage + '%');
        }

        function animateKPICards() {
            const kpiCards = document.querySelectorAll('.kpi-card');
            kpiCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart');
            if (revenueCtx && revenueData.length > 0) {
                new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: revenueData.map(d => d.month),
                        datasets: [{
                            label: 'Revenue',
                            data: revenueData.map(d => d.revenue),
                            borderColor: '#4f46e5',
                            backgroundColor: 'rgba(79, 70, 229, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#4f46e5',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Project Status Chart
            const statusCtx = document.getElementById('projectStatusChart');
            if (statusCtx && projectStatusData.length > 0) {
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: projectStatusData.map(d => d.status),
                        datasets: [{
                            data: projectStatusData.map(d => d.count),
                            backgroundColor: ['#4f46e5', '#10b981'],
                            borderWidth: 0,
                            cutout: '70%'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Revenue Sparkline
            const sparklineCtx = document.getElementById('revenueSparkline');
            if (sparklineCtx && revenueData.length > 0) {
                new Chart(sparklineCtx, {
                    type: 'line',
                    data: {
                        labels: revenueData.map(d => d.month),
                        datasets: [{
                            data: revenueData.map(d => d.revenue),
                            borderColor: '#10b981',
                            borderWidth: 2,
                            fill: false,
                            pointRadius: 0,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        }
                    }
                });
            }
        }
    </script>
}
