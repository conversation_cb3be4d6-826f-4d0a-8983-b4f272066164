using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Models;

// DTOs for API responses
public class ServiceDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ShortDescription { get; set; } = string.Empty;
    public string IconClass { get; set; } = string.Empty;
    public decimal BasePrice { get; set; }
    public string PricingModel { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public string Tags { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public int EstimatedDeliveryDays { get; set; }
    public List<ServiceFeatureDto> Features { get; set; } = new();
    public List<ServicePricingTierDto> PricingTiers { get; set; } = new();
}

public class ServiceFeatureDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string FeatureType { get; set; } = string.Empty;
    public decimal AdditionalPrice { get; set; }
    public string PricingType { get; set; } = string.Empty;
    public string IconClass { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public string Category { get; set; } = string.Empty;
}

public class ServicePricingTierDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string Features { get; set; } = string.Empty;
    public int MinQuantity { get; set; }
    public int? MaxQuantity { get; set; }
    public int DeliveryDays { get; set; }
    public bool IsPopular { get; set; }
    public string BadgeText { get; set; } = string.Empty;
    public string BadgeColor { get; set; } = string.Empty;
}

public class QuoteRequestDto
{
    [Required(ErrorMessage = "Client name is required")]
    public string ClientName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    public string ClientEmail { get; set; } = string.Empty;
    
    public string ClientPhone { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public string ProjectDescription { get; set; } = string.Empty;
    public List<QuoteItemRequestDto> Items { get; set; } = new();
}

public class QuoteItemRequestDto
{
    public int ServiceId { get; set; }
    public int? ServicePricingTierId { get; set; }
    public int Quantity { get; set; } = 1;
    public string Configuration { get; set; } = string.Empty;
    public List<QuoteItemFeatureRequestDto> SelectedFeatures { get; set; } = new();
}

public class QuoteItemFeatureRequestDto
{
    public int ServiceFeatureId { get; set; }
    public int Quantity { get; set; } = 1;
    public string Configuration { get; set; } = string.Empty;
}

public class QuoteResponseDto
{
    public int Id { get; set; }
    public string QuoteNumber { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public string ClientEmail { get; set; } = string.Empty;
    public decimal Subtotal { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string Status { get; set; } = string.Empty;
    public int EstimatedDeliveryDays { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<QuoteItemDto> Items { get; set; } = new();
}

public class QuoteItemDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public int DeliveryDays { get; set; }
    public ServiceDto Service { get; set; } = new();
    public ServicePricingTierDto? ServicePricingTier { get; set; }
    public List<QuoteItemFeatureDto> SelectedFeatures { get; set; } = new();
}

public class QuoteItemFeatureDto
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public ServiceFeatureDto ServiceFeature { get; set; } = new();
}

// ViewModels for MVC views
public class PricingIndexViewModel
{
    public List<ServiceDto> Services { get; set; } = new();
    public List<ServiceDto> FeaturedServices { get; set; } = new();
    public string? SelectedCategory { get; set; }
    public List<string> Categories { get; set; } = new();
}

public class ServiceConfigurationViewModel
{
    public ServiceDto Service { get; set; } = new();
    public int SelectedTierId { get; set; }
    public Dictionary<int, bool> SelectedFeatures { get; set; } = new();
    public Dictionary<int, int> FeatureQuantities { get; set; } = new();
    public int Quantity { get; set; } = 1;
    public decimal CalculatedPrice { get; set; }
    public int EstimatedDeliveryDays { get; set; }
}

public class QuoteBuilderViewModel
{
    public List<ServiceConfigurationViewModel> SelectedServices { get; set; } = new();
    public decimal Subtotal { get; set; }
    public decimal TaxRate { get; set; } = 0;
    public decimal TaxAmount { get; set; }
    public decimal DiscountPercentage { get; set; } = 0;
    public decimal DiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public int EstimatedDeliveryDays { get; set; }
    public QuoteRequestDto QuoteRequest { get; set; } = new();
}

public class PriceCalculationRequest
{
    public int ServiceId { get; set; }
    public int? TierId { get; set; }
    public int Quantity { get; set; } = 1;
    public List<int> SelectedFeatureIds { get; set; } = new();
    public Dictionary<int, int> FeatureQuantities { get; set; } = new();
}

public class PriceCalculationResponse
{
    public decimal BasePrice { get; set; }
    public decimal FeaturesPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public int EstimatedDeliveryDays { get; set; }
    public List<PriceBreakdownItem> Breakdown { get; set; } = new();
}

public class PriceBreakdownItem
{
    public string Name { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string Type { get; set; } = string.Empty; // Service, Feature, Tier
}
