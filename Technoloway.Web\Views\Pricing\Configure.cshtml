@model Technoloway.Web.Models.ServiceConfigurationViewModel
@{
    ViewData["Title"] = $"Configure {Model.Service.Name} - Get Your Quote";
    ViewData["Description"] = $"Configure and customize {Model.Service.Name} service to get an accurate price quote.";
}

@section Styles {
    <style>
        .service-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .configuration-card {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 15px;
        }
        .pricing-summary {
            position: sticky;
            top: 100px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
        }
        .feature-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .feature-card:hover {
            border-color: var(--bs-primary);
            transform: translateY(-2px);
        }
        .feature-card.selected {
            border-color: var(--bs-primary);
            background-color: rgba(var(--bs-primary-rgb), 0.1);
        }
        .feature-card.required {
            border-color: var(--bs-success);
            background-color: rgba(var(--bs-success-rgb), 0.1);
        }
        .tier-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .tier-card:hover {
            border-color: var(--bs-primary);
        }
        .tier-card.selected {
            border-color: var(--bs-primary);
            background-color: rgba(var(--bs-primary-rgb), 0.1);
        }
        .tier-card.popular {
            border-color: var(--bs-warning);
            position: relative;
        }
        .tier-card.popular::before {
            content: attr(data-badge);
            position: absolute;
            top: -10px;
            right: 15px;
            background: var(--bs-warning);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .price-breakdown {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        .quantity-input {
            max-width: 100px;
        }
        .loading-overlay {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            z-index: 1000;
        }
    </style>
}

<!-- Service Header -->
<section class="service-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="d-flex align-items-center mb-3">
                    @if (!string.IsNullOrEmpty(Model.Service.IconClass))
                    {
                        <i class="@Model.Service.IconClass fa-3x me-3"></i>
                    }
                    <div>
                        <h1 class="fw-bold mb-2">@Model.Service.Name</h1>
                        <p class="mb-0 opacity-75">@Model.Service.ShortDescription</p>
                    </div>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")" class="text-white-50">Home</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")" class="text-white-50">Pricing</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Configure @Model.Service.Name</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="text-white">
                    <div class="h4 mb-1">Starting from</div>
                    <div class="display-6 fw-bold">$@Model.Service.BasePrice.ToString("N0")</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Configuration Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Configuration Options -->
            <div class="col-lg-8">
                <div class="configuration-card card mb-4">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-4">Configure Your Service</h3>
                        
                        <!-- Service Description -->
                        <div class="mb-4">
                            <h5>About This Service</h5>
                            <p class="text-muted">@Model.Service.Description</p>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="mb-4">
                            <label for="quantity" class="form-label fw-bold">Quantity</label>
                            <div class="d-flex align-items-center">
                                <input type="number" id="quantity" class="form-control quantity-input" value="@Model.Quantity" min="1" max="10">
                                <span class="ms-2 text-muted">projects</span>
                            </div>
                        </div>

                        <!-- Pricing Tiers -->
                        @if (Model.Service.PricingTiers.Any())
                        {
                            <div class="mb-4">
                                <h5 class="mb-3">Choose Your Plan</h5>
                                <div class="row g-3">
                                    @foreach (var tier in Model.Service.PricingTiers.OrderBy(t => t.Price))
                                    {
                                        <div class="col-md-6 col-lg-4">
                                            <div class="tier-card card h-100 @(tier.IsPopular ? "popular" : "")" 
                                                 data-tier-id="@tier.Id" 
                                                 data-badge="@tier.BadgeText">
                                                <div class="card-body text-center p-3">
                                                    <h6 class="card-title">@tier.Name</h6>
                                                    <div class="h4 text-primary mb-2">$@tier.Price.ToString("N0")</div>
                                                    <p class="small text-muted mb-3">@tier.Description</p>
                                                    <div class="small">
                                                        <i class="fas fa-clock text-muted"></i> @tier.DeliveryDays days
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }

                        <!-- Features Selection -->
                        @if (Model.Service.Features.Any())
                        {
                            <div class="mb-4">
                                <h5 class="mb-3">Additional Features</h5>
                                
                                <!-- Required Features -->
                                @{
                                    var requiredFeatures = Model.Service.Features.Where(f => f.IsRequired).ToList();
                                    var optionalFeatures = Model.Service.Features.Where(f => !f.IsRequired).ToList();
                                }
                                
                                @if (requiredFeatures.Any())
                                {
                                    <h6 class="text-success mb-3">Included Features</h6>
                                    <div class="row g-3 mb-4">
                                        @foreach (var feature in requiredFeatures)
                                        {
                                            <div class="col-md-6">
                                                <div class="feature-card card required">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex align-items-start">
                                                            @if (!string.IsNullOrEmpty(feature.IconClass))
                                                            {
                                                                <i class="@feature.IconClass text-success me-2 mt-1"></i>
                                                            }
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-1">@feature.Name</h6>
                                                                <p class="small text-muted mb-0">@feature.Description</p>
                                                                <span class="badge bg-success">Included</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }

                                @if (optionalFeatures.Any())
                                {
                                    <h6 class="text-primary mb-3">Optional Add-ons</h6>
                                    <div class="row g-3">
                                        @foreach (var feature in optionalFeatures)
                                        {
                                            <div class="col-md-6">
                                                <div class="feature-card card" data-feature-id="@feature.Id" data-price="@feature.AdditionalPrice">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex align-items-start">
                                                            <div class="form-check me-2 mt-1">
                                                                <input class="form-check-input feature-checkbox" type="checkbox" 
                                                                       id="<EMAIL>" data-feature-id="@feature.Id">
                                                            </div>
                                                            @if (!string.IsNullOrEmpty(feature.IconClass))
                                                            {
                                                                <i class="@feature.IconClass text-primary me-2 mt-1"></i>
                                                            }
                                                            <div class="flex-grow-1">
                                                                <label for="<EMAIL>" class="form-check-label">
                                                                    <h6 class="mb-1">@feature.Name</h6>
                                                                    <p class="small text-muted mb-2">@feature.Description</p>
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <span class="fw-bold text-primary">+$@feature.AdditionalPrice.ToString("N0")</span>
                                                                        @if (feature.PricingType != "OneTime")
                                                                        {
                                                                            <span class="badge bg-info">@feature.PricingType</span>
                                                                        }
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Pricing Summary -->
            <div class="col-lg-4">
                <div class="pricing-summary">
                    <div class="position-relative">
                        <div class="loading-overlay d-flex align-items-center justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Calculating...</span>
                            </div>
                        </div>
                        
                        <h4 class="mb-3">Price Summary</h4>
                        
                        <div class="price-breakdown">
                            <div id="price-breakdown-content">
                                <!-- Price breakdown will be populated by JavaScript -->
                            </div>
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="fw-bold">Total Price:</span>
                                <span class="h4 text-primary fw-bold" id="total-price">$@Model.CalculatedPrice.ToString("N0")</span>
                            </div>
                            
                            <div class="text-center mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> 
                                    Estimated delivery: <span id="delivery-days">@Model.EstimatedDeliveryDays</span> days
                                </small>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary btn-lg" id="add-to-quote">
                                    <i class="fas fa-plus me-2"></i>Add to Quote
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="get-instant-quote">
                                    <i class="fas fa-calculator me-2"></i>Get Instant Quote
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        class ServiceConfigurator {
            constructor() {
                this.serviceId = @Model.Service.Id;
                this.selectedTierId = null;
                this.selectedFeatures = new Set();
                this.quantity = @Model.Quantity;
                
                this.initializeEventListeners();
                this.calculatePrice();
            }
            
            initializeEventListeners() {
                // Quantity change
                document.getElementById('quantity').addEventListener('change', (e) => {
                    this.quantity = parseInt(e.target.value) || 1;
                    this.calculatePrice();
                });
                
                // Tier selection
                document.querySelectorAll('.tier-card').forEach(card => {
                    card.addEventListener('click', () => {
                        document.querySelectorAll('.tier-card').forEach(c => c.classList.remove('selected'));
                        card.classList.add('selected');
                        this.selectedTierId = parseInt(card.dataset.tierId);
                        this.calculatePrice();
                    });
                });
                
                // Feature selection
                document.querySelectorAll('.feature-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', (e) => {
                        const featureId = parseInt(e.target.dataset.featureId);
                        const card = e.target.closest('.feature-card');
                        
                        if (e.target.checked) {
                            this.selectedFeatures.add(featureId);
                            card.classList.add('selected');
                        } else {
                            this.selectedFeatures.delete(featureId);
                            card.classList.remove('selected');
                        }
                        
                        this.calculatePrice();
                    });
                });
                
                // Feature card click (toggle checkbox)
                document.querySelectorAll('.feature-card[data-feature-id]').forEach(card => {
                    card.addEventListener('click', (e) => {
                        if (e.target.type !== 'checkbox') {
                            const checkbox = card.querySelector('.feature-checkbox');
                            checkbox.click();
                        }
                    });
                });
                
                // Add to quote button
                document.getElementById('add-to-quote').addEventListener('click', () => {
                    this.addToQuote();
                });
                
                // Get instant quote button
                document.getElementById('get-instant-quote').addEventListener('click', () => {
                    this.getInstantQuote();
                });
            }
            
            async calculatePrice() {
                const loadingOverlay = document.querySelector('.loading-overlay');
                loadingOverlay.style.display = 'flex';
                
                try {
                    const request = {
                        serviceId: this.serviceId,
                        tierId: this.selectedTierId,
                        quantity: this.quantity,
                        selectedFeatureIds: Array.from(this.selectedFeatures),
                        featureQuantities: {}
                    };
                    
                    // Set feature quantities (default to 1 for now)
                    this.selectedFeatures.forEach(featureId => {
                        request.featureQuantities[featureId] = 1;
                    });
                    
                    const response = await fetch('@Url.Action("CalculatePrice")', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                        },
                        body: JSON.stringify(request)
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        this.updatePricingDisplay(result);
                    } else {
                        console.error('Error calculating price:', response.statusText);
                    }
                } catch (error) {
                    console.error('Error calculating price:', error);
                } finally {
                    loadingOverlay.style.display = 'none';
                }
            }
            
            updatePricingDisplay(priceData) {
                // Update total price
                document.getElementById('total-price').textContent = `$${priceData.totalPrice.toLocaleString()}`;
                
                // Update delivery days
                document.getElementById('delivery-days').textContent = priceData.estimatedDeliveryDays;
                
                // Update price breakdown
                const breakdownContent = document.getElementById('price-breakdown-content');
                breakdownContent.innerHTML = '';
                
                priceData.breakdown.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'd-flex justify-content-between mb-2';
                    div.innerHTML = `
                        <span>${item.name} ${item.quantity > 1 ? `(×${item.quantity})` : ''}</span>
                        <span>$${item.totalPrice.toLocaleString()}</span>
                    `;
                    breakdownContent.appendChild(div);
                });
            }
            
            addToQuote() {
                // Store configuration in session storage for quote builder
                const configuration = {
                    serviceId: this.serviceId,
                    tierId: this.selectedTierId,
                    quantity: this.quantity,
                    selectedFeatures: Array.from(this.selectedFeatures)
                };
                
                let quoteItems = JSON.parse(sessionStorage.getItem('quoteItems') || '[]');
                quoteItems.push(configuration);
                sessionStorage.setItem('quoteItems', JSON.stringify(quoteItems));
                
                // Show success message
                const button = document.getElementById('add-to-quote');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-2"></i>Added to Quote';
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                }, 2000);
            }
            
            getInstantQuote() {
                // Redirect to quote builder with current configuration
                const configuration = {
                    serviceId: this.serviceId,
                    tierId: this.selectedTierId,
                    quantity: this.quantity,
                    selectedFeatures: Array.from(this.selectedFeatures)
                };
                
                sessionStorage.setItem('instantQuoteConfig', JSON.stringify(configuration));
                window.location.href = '@Url.Action("QuoteBuilder")';
            }
        }
        
        // Initialize configurator when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ServiceConfigurator();
        });
    </script>
}
