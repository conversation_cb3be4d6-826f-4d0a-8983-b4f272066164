@model Technoloway.Web.Models.QuoteBuilderViewModel
@{
    ViewData["Title"] = "Quote Builder - Get Your Custom Quote";
    ViewData["Description"] = "Build your custom software development quote by selecting services and features that match your needs.";
}

@section Styles {
    <style>
        .quote-builder-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background: var(--bs-primary);
            color: white;
        }
        .step.completed .step-number {
            background: var(--bs-success);
            color: white;
        }
        .quote-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
        }
        .quote-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            position: sticky;
            top: 100px;
        }
        .service-selector {
            max-height: 400px;
            overflow-y: auto;
        }
        .service-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .service-card:hover {
            border-color: var(--bs-primary);
            transform: translateY(-2px);
        }
        .service-card.selected {
            border-color: var(--bs-primary);
            background-color: rgba(var(--bs-primary-rgb), 0.1);
        }
        .client-form {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .quote-step {
            display: none;
        }
        .quote-step.active {
            display: block;
        }
        .floating-total {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--bs-primary);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        @@media (max-width: 768px) {
            .floating-total {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: 1rem;
            }
        }
    </style>
}

<!-- Header -->
<section class="quote-builder-header">
    <div class="container">
        <div class="text-center">
            <h1 class="display-5 fw-bold mb-3">Build Your Custom Quote</h1>
            <p class="lead mb-0">Select services, configure options, and get an instant quote for your project</p>
        </div>
    </div>
</section>

<!-- Step Indicator -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="step-indicator">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <span>Select Services</span>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <span>Configure</span>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <span>Your Details</span>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <span>Review & Submit</span>
            </div>
        </div>
    </div>
</section>

<!-- Quote Builder Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Step 1: Service Selection -->
                <div class="quote-step active" id="step-1">
                    <div class="card">
                        <div class="card-body p-4">
                            <h3 class="card-title mb-4">Select Services</h3>
                            <div class="service-selector" id="service-list">
                                <!-- Services will be loaded here -->
                            </div>
                            <div class="text-end mt-4">
                                <button type="button" class="btn btn-primary" id="next-to-configure" disabled>
                                    Next: Configure Services <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Configuration -->
                <div class="quote-step" id="step-2">
                    <div class="card">
                        <div class="card-body p-4">
                            <h3 class="card-title mb-4">Configure Your Services</h3>
                            <div id="selected-services">
                                <!-- Selected services configuration will appear here -->
                            </div>
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-outline-secondary" id="back-to-services">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Services
                                </button>
                                <button type="button" class="btn btn-primary" id="next-to-details">
                                    Next: Your Details <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Client Details -->
                <div class="quote-step" id="step-3">
                    <div class="client-form">
                        <h3 class="mb-4">Your Details</h3>
                        <form id="client-form">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="clientName" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="clientName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="clientEmail" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="clientEmail" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="clientPhone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="clientPhone">
                                </div>
                                <div class="col-md-6">
                                    <label for="companyName" class="form-label">Company Name</label>
                                    <input type="text" class="form-control" id="companyName">
                                </div>
                                <div class="col-12">
                                    <label for="projectDescription" class="form-label">Project Description</label>
                                    <textarea class="form-control" id="projectDescription" rows="4" 
                                              placeholder="Tell us about your project requirements, goals, and any specific needs..."></textarea>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-outline-secondary" id="back-to-configure">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Configure
                                </button>
                                <button type="button" class="btn btn-primary" id="next-to-review">
                                    Next: Review Quote <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Step 4: Review & Submit -->
                <div class="quote-step" id="step-4">
                    <div class="card">
                        <div class="card-body p-4">
                            <h3 class="card-title mb-4">Review Your Quote</h3>
                            
                            <!-- Client Details Summary -->
                            <div class="mb-4">
                                <h5>Contact Information</h5>
                                <div id="client-summary" class="bg-light p-3 rounded">
                                    <!-- Client details will be populated here -->
                                </div>
                            </div>

                            <!-- Services Summary -->
                            <div class="mb-4">
                                <h5>Selected Services</h5>
                                <div id="services-summary">
                                    <!-- Services summary will be populated here -->
                                </div>
                            </div>

                            <!-- Final Price -->
                            <div class="bg-primary text-white p-4 rounded mb-4">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h4 class="mb-1">Total Project Cost</h4>
                                        <p class="mb-0 opacity-75">Estimated delivery: <span id="final-delivery-days">30</span> days</p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="display-6 fw-bold" id="final-total-price">$0</div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" id="back-to-details">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Details
                                </button>
                                <button type="button" class="btn btn-success btn-lg" id="submit-quote">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Quote Request
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quote Summary Sidebar -->
            <div class="col-lg-4">
                <div class="quote-summary">
                    <h4 class="mb-3">Quote Summary</h4>
                    <div id="quote-items">
                        <p class="text-muted text-center py-4">No services selected yet</p>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold">Total:</span>
                        <span class="h4 text-primary fw-bold" id="sidebar-total">$0</span>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Estimated delivery: <span id="sidebar-delivery">0</span> days
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Floating Total (Mobile) -->
<div class="floating-total d-lg-none">
    <div class="d-flex align-items-center">
        <div class="me-3">
            <div class="fw-bold">Total: <span id="mobile-total">$0</span></div>
            <small>Est. <span id="mobile-delivery">0</span> days</small>
        </div>
        <button type="button" class="btn btn-light btn-sm" onclick="document.querySelector('.quote-summary').scrollIntoView()">
            View Details
        </button>
    </div>
</div>

@section Scripts {
    <script src="~/js/quote-builder.js"></script>
    <script>
        // Initialize quote builder when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const quoteBuilder = new QuoteBuilder();
            
            // Check for instant quote configuration from service configure page
            const instantConfig = sessionStorage.getItem('instantQuoteConfig');
            if (instantConfig) {
                const config = JSON.parse(instantConfig);
                quoteBuilder.addServiceFromConfig(config);
                sessionStorage.removeItem('instantQuoteConfig');
            }
            
            // Check for quote items from service configure page
            const quoteItems = sessionStorage.getItem('quoteItems');
            if (quoteItems) {
                const items = JSON.parse(quoteItems);
                items.forEach(item => quoteBuilder.addServiceFromConfig(item));
                sessionStorage.removeItem('quoteItems');
            }
        });
    </script>
}
