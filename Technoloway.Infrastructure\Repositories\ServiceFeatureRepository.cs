using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories;

public class ServiceFeatureRepository : Repository<ServiceFeature>, IServiceFeatureRepository
{
    public ServiceFeatureRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<ServiceFeature>> GetByServiceIdAsync(int serviceId)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.ServiceId == serviceId && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<ServiceFeature>> GetActiveByServiceIdAsync(int serviceId)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.ServiceId == serviceId && sf.IsActive && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<ServiceFeature>> GetByFeatureTypeAsync(string featureType)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.FeatureType == featureType && sf.IsActive && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<ServiceFeature>> GetByCategoryAsync(string category)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.Category == category && sf.IsActive && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<ServiceFeature>> GetRequiredFeaturesAsync(int serviceId)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.ServiceId == serviceId && sf.IsRequired && sf.IsActive && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<ServiceFeature>> GetOptionalFeaturesAsync(int serviceId)
    {
        return await _context.ServiceFeatures
            .Where(sf => sf.ServiceId == serviceId && !sf.IsRequired && sf.IsActive && !sf.IsDeleted)
            .OrderBy(sf => sf.DisplayOrder)
            .ThenBy(sf => sf.Name)
            .ToListAsync();
    }

    public async Task<decimal> CalculateFeaturesTotalAsync(IEnumerable<int> featureIds, Dictionary<int, int> quantities)
    {
        var features = await _context.ServiceFeatures
            .Where(sf => featureIds.Contains(sf.Id) && sf.IsActive && !sf.IsDeleted)
            .ToListAsync();

        decimal total = 0;
        foreach (var feature in features)
        {
            var quantity = quantities.ContainsKey(feature.Id) ? quantities[feature.Id] : 1;
            total += feature.AdditionalPrice * quantity;
        }

        return total;
    }
}
