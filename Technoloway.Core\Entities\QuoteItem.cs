using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class QuoteItem : BaseEntity
{
    [Required(ErrorMessage = "Item name is required")]
    [StringLength(200, ErrorMessage = "Item name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Item description cannot exceed 1000 characters")]
    public string Description { get; set; } = string.Empty;
    
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; } = 1;
    
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Total price must be greater than or equal to 0")]
    public decimal TotalPrice { get; set; }
    
    [Range(1, 365, ErrorMessage = "Delivery days must be between 1 and 365")]
    public int DeliveryDays { get; set; } = 30;
    
    [StringLength(1000, ErrorMessage = "Configuration cannot exceed 1000 characters")]
    public string Configuration { get; set; } = string.Empty; // JSON string of selected options
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    // Foreign keys
    [Required(ErrorMessage = "Quote is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid quote")]
    public int QuoteId { get; set; }
    
    [Required(ErrorMessage = "Service is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service")]
    public int ServiceId { get; set; }
    
    public int? ServicePricingTierId { get; set; }
    
    // Navigation properties
    public Quote Quote { get; set; } = null!;
    public Service Service { get; set; } = null!;
    public ServicePricingTier? ServicePricingTier { get; set; }
    public ICollection<QuoteItemFeature> SelectedFeatures { get; set; } = new List<QuoteItemFeature>();
}
