@model IEnumerable<Technoloway.Core.Entities.ServicePricingTier>
@{
    ViewData["Title"] = "Pricing Tiers - " + ViewBag.Service.Name;
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
    var service = ViewBag.Service as Technoloway.Core.Entities.Service;
}

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("Index")">Pricing Management</a>
                    </li>
                    <li class="breadcrumb-item active">@service.Name - Pricing Tiers</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-layer-group me-2"></i>Pricing Tiers
            </h1>
            <p class="text-muted mb-0">Manage pricing tiers for <strong>@service.Name</strong></p>
        </div>
        <div>
            <a href="@Url.Action("CreateTier", new { serviceId = service.Id })" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add New Tier
            </a>
        </div>
    </div>

    <!-- Service Info Card -->
    <div class="card mb-4 border-left-primary">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="text-primary mb-1">@service.Name</h5>
                    <p class="text-muted mb-0">@service.ShortDescription</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex justify-content-md-end gap-2">
                        <span class="badge bg-info">Base: @service.BasePrice.ToString("C")</span>
                        <span class="badge bg-success">@service.EstimatedDeliveryDays days</span>
                        @if (service.IsFeatured)
                        {
                            <span class="badge bg-warning text-dark">Featured</span>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (Model.Any())
    {
        <!-- Pricing Tiers Grid -->
        <div class="row">
            @foreach (var tier in Model.OrderBy(t => t.DisplayOrder))
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 @(tier.IsPopular ? "border-primary" : "")">
                        @if (tier.IsPopular)
                        {
                            <div class="card-header bg-primary text-white text-center py-2">
                                <small><i class="fas fa-star me-1"></i>@tier.BadgeText</small>
                            </div>
                        }
                        else if (!string.IsNullOrEmpty(tier.BadgeText))
                        {
                            <div class="card-header <EMAIL> text-white text-center py-2">
                                <small>@tier.BadgeText</small>
                            </div>
                        }

                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">@tier.Name</h5>
                            <div class="mb-3">
                                <span class="h2 text-dark">@tier.Price.ToString("C")</span>
                                @if (tier.MinQuantity > 1 || tier.MaxQuantity.HasValue)
                                {
                                    <div class="text-muted small">
                                        Qty: @tier.MinQuantity@(tier.MaxQuantity.HasValue ? $" - {tier.MaxQuantity}" : "+")
                                    </div>
                                }
                            </div>
                            <p class="text-muted">@tier.Description</p>
                            
                            @if (!string.IsNullOrEmpty(tier.Features))
                            {
                                <div class="text-start mb-3">
                                    <small class="text-muted">Features:</small>
                                    <div class="small">
                                        @foreach (var feature in tier.Features.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                        {
                                            <div><i class="fas fa-check text-success me-1"></i>@feature.Trim()</div>
                                        }
                                    </div>
                                </div>
                            }

                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="border-end">
                                        <small class="text-muted d-block">Delivery</small>
                                        <strong>@tier.DeliveryDays days</strong>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">Order</small>
                                    <strong>#@tier.DisplayOrder</strong>
                                </div>
                            </div>

                            <div class="d-flex justify-content-center gap-1">
                                <span class="badge bg-@(tier.IsActive ? "success" : "secondary")">
                                    @(tier.IsActive ? "Active" : "Inactive")
                                </span>
                                @if (tier.IsPopular)
                                {
                                    <span class="badge bg-warning text-dark">Popular</span>
                                }
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-grid gap-2">
                                <div class="btn-group" role="group">
                                    <a href="@Url.Action("EditTier", new { id = tier.Id })" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="confirmDelete(@tier.Id, '@tier.Name')">
                                        <i class="fas fa-trash me-1"></i>Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Pricing Tiers Found</h5>
                <p class="text-muted">Create pricing tiers to offer different packages for this service.</p>
                <a href="@Url.Action("CreateTier", new { serviceId = service.Id })" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create First Tier
                </a>
            </div>
        </div>
    }
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the pricing tier "<span id="tierName"></span>"?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(tierId, tierName) {
            document.getElementById('tierName').textContent = tierName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteTier")';
            
            // Add hidden input for tier ID
            const existingInput = document.querySelector('#deleteForm input[name="id"]');
            if (existingInput) {
                existingInput.remove();
            }
            
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'id';
            hiddenInput.value = tierId;
            document.getElementById('deleteForm').appendChild(hiddenInput);
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
