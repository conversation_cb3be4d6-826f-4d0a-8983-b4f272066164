using Microsoft.AspNetCore.Mvc;
using Technoloway.Web.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Controllers.Api;

[ApiController]
[Route("api/[controller]")]
public class PricingApiController : ControllerBase
{
    private readonly IPricingService _pricingService;
    private readonly ILogger<PricingApiController> _logger;

    public PricingApiController(IPricingService pricingService, ILogger<PricingApiController> logger)
    {
        _pricingService = pricingService;
        _logger = logger;
    }

    /// <summary>
    /// Get all services with pricing information
    /// </summary>
    [HttpGet("services")]
    public async Task<ActionResult<IEnumerable<ServiceDto>>> GetServices()
    {
        try
        {
            var services = await _pricingService.GetServicesWithPricingAsync();
            return Ok(services);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving services");
            return StatusCode(500, new { message = "An error occurred while retrieving services" });
        }
    }

    /// <summary>
    /// Get a specific service with pricing information
    /// </summary>
    [HttpGet("services/{id}")]
    public async Task<ActionResult<ServiceDto>> GetService(int id)
    {
        try
        {
            var service = await _pricingService.GetServiceWithPricingAsync(id);
            if (service == null)
            {
                return NotFound(new { message = "Service not found" });
            }
            return Ok(service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service {ServiceId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the service" });
        }
    }

    /// <summary>
    /// Calculate price for a service configuration
    /// </summary>
    [HttpPost("calculate")]
    public async Task<ActionResult<PriceCalculationResponse>> CalculatePrice([FromBody] PriceCalculationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _pricingService.CalculatePriceAsync(request);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating price for service {ServiceId}", request.ServiceId);
            return StatusCode(500, new { message = "An error occurred while calculating the price" });
        }
    }

    /// <summary>
    /// Create a new quote
    /// </summary>
    [HttpPost("quotes")]
    public async Task<ActionResult<QuoteResponseDto>> CreateQuote([FromBody] QuoteRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var quote = await _pricingService.CreateQuoteAsync(request);
            return CreatedAtAction(nameof(GetQuote), new { id = quote.Id }, quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating quote for {ClientEmail}", request.ClientEmail);
            return StatusCode(500, new { message = "An error occurred while creating the quote" });
        }
    }

    /// <summary>
    /// Get a quote by ID
    /// </summary>
    [HttpGet("quotes/{id}")]
    public async Task<ActionResult<QuoteResponseDto>> GetQuote(int id)
    {
        try
        {
            var quote = await _pricingService.GetQuoteAsync(id);
            if (quote == null)
            {
                return NotFound(new { message = "Quote not found" });
            }
            return Ok(quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving quote {QuoteId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the quote" });
        }
    }

    /// <summary>
    /// Get a quote by quote number
    /// </summary>
    [HttpGet("quotes/by-number/{quoteNumber}")]
    public async Task<ActionResult<QuoteResponseDto>> GetQuoteByNumber(string quoteNumber)
    {
        try
        {
            var quote = await _pricingService.GetQuoteByNumberAsync(quoteNumber);
            if (quote == null)
            {
                return NotFound(new { message = "Quote not found" });
            }
            return Ok(quote);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving quote {QuoteNumber}", quoteNumber);
            return StatusCode(500, new { message = "An error occurred while retrieving the quote" });
        }
    }

    /// <summary>
    /// Update quote status
    /// </summary>
    [HttpPatch("quotes/{id}/status")]
    public async Task<ActionResult> UpdateQuoteStatus(int id, [FromBody] UpdateQuoteStatusRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var success = await _pricingService.UpdateQuoteStatusAsync(id, request.Status);
            if (!success)
            {
                return NotFound(new { message = "Quote not found" });
            }

            return Ok(new { message = "Quote status updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quote status for {QuoteId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the quote status" });
        }
    }
}

public class UpdateQuoteStatusRequest
{
    public string Status { get; set; } = string.Empty;
}
