@model IEnumerable<Technoloway.Core.Entities.Service>
@{
    ViewData["Title"] = "Pricing Management";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-dollar-sign me-2"></i>Pricing Management
            </h1>
            <p class="text-muted mb-0">Manage service features, pricing tiers, and quotes</p>
        </div>
        <div>
            <a href="@Url.Action("Analytics")" class="btn btn-info">
                <i class="fas fa-chart-bar me-1"></i>Analytics
            </a>
            <a href="@Url.Action("Quotes")" class="btn btn-primary">
                <i class="fas fa-file-invoice me-1"></i>View Quotes
            </a>
        </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card primary h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon primary me-3">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Services</p>
                        <h3 class="admin-stat-number">@ViewBag.TotalServices</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Features</p>
                        <h3 class="admin-stat-number">@ViewBag.TotalFeatures</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Pricing Tiers</p>
                        <h3 class="admin-stat-number">@ViewBag.TotalTiers</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Quotes</p>
                        <h3 class="admin-stat-number">@ViewBag.TotalQuotes</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Management -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-cogs me-2"></i>Services & Pricing Configuration
            </h6>
        </div>
        <div class="card-body">
            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var service in Model)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 border-left-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h5 class="card-title text-primary mb-1">@service.Name</h5>
                                            <p class="text-muted small mb-0">@service.ShortDescription</p>
                                        </div>
                                        @if (service.IsFeatured)
                                        {
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        }
                                    </div>

                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="text-primary mb-0">@service.BasePrice.ToString("C")</h6>
                                                <small class="text-muted">Base Price</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="text-success mb-0">@service.EstimatedDeliveryDays days</h6>
                                            <small class="text-muted">Delivery</small>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <div class="btn-group" role="group">
                                            <a href="@Url.Action("ServiceFeatures", new { serviceId = service.Id })" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-list me-1"></i>Features
                                            </a>
                                            <a href="@Url.Action("PricingTiers", new { serviceId = service.Id })" 
                                               class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-layer-group me-1"></i>Tiers
                                            </a>
                                        </div>
                                        <a href="@Url.Action("Details", "Services", new { id = service.Id })" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Service
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Services Found</h5>
                    <p class="text-muted">Create services first to manage their pricing features and tiers.</p>
                    <a href="@Url.Action("Create", "Services")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create Service
                    </a>
                </div>
            }
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-file-invoice fa-2x text-info"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-info mb-1">Quote Management</h6>
                            <p class="text-muted mb-2">View and manage customer quotes</p>
                            <a href="@Url.Action("Quotes")" class="btn btn-info btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>Manage Quotes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-chart-bar fa-2x text-success"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-success mb-1">Pricing Analytics</h6>
                            <p class="text-muted mb-2">View pricing performance and statistics</p>
                            <a href="@Url.Action("Analytics")" class="btn btn-success btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>View Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            // You can add AJAX calls here to refresh stats if needed
        }, 30000);
    </script>
}
