using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class ServicePricingTier : BaseEntity
{
    [Required(ErrorMessage = "Tier name is required")]
    [StringLength(100, ErrorMessage = "Tier name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "Tier description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;
    
    [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
    public decimal Price { get; set; }
    
    [StringLength(1000, ErrorMessage = "Features list cannot exceed 1000 characters")]
    public string Features { get; set; } = string.Empty; // JSON string of features
    
    [Range(0, int.MaxValue, ErrorMessage = "Min quantity must be a positive number")]
    public int MinQuantity { get; set; } = 1;
    
    [Range(0, int.MaxValue, ErrorMessage = "Max quantity must be a positive number")]
    public int? MaxQuantity { get; set; }
    
    [Range(1, 365, ErrorMessage = "Delivery days must be between 1 and 365")]
    public int DeliveryDays { get; set; } = 30;
    
    public bool IsActive { get; set; } = true;
    public bool IsPopular { get; set; } = false;
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    [StringLength(50, ErrorMessage = "Badge text cannot exceed 50 characters")]
    public string BadgeText { get; set; } = string.Empty; // e.g., "Most Popular", "Best Value"
    
    [StringLength(20, ErrorMessage = "Badge color cannot exceed 20 characters")]
    public string BadgeColor { get; set; } = "primary"; // CSS class for badge color
    
    // Foreign key
    [Required(ErrorMessage = "Service is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service")]
    public int ServiceId { get; set; }
    
    // Navigation properties
    public Service Service { get; set; } = null!;
    public ICollection<QuoteItem> QuoteItems { get; set; } = new List<QuoteItem>();
}
