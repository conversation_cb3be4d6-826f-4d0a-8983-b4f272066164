@model Technoloway.Core.Entities.ServicePricingTier
@{
    ViewData["Title"] = "Edit Pricing Tier";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
    var service = ViewBag.Service as Technoloway.Core.Entities.Service;
}

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("Index")">Pricing Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("PricingTiers", new { serviceId = service.Id })">@service.Name - Tiers</a>
                    </li>
                    <li class="breadcrumb-item active">Edit @Model.Name</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit me-2"></i>Edit Pricing Tier
            </h1>
            <p class="text-muted mb-0">Modify pricing tier for <strong>@service.Name</strong></p>
        </div>
        <div>
            <a href="@Url.Action("PricingTiers", new { serviceId = service.Id })" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Tiers
            </a>
        </div>
    </div>

    <!-- Service Info -->
    <div class="card mb-4 border-left-primary">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="text-primary mb-1">@service.Name</h6>
                    <p class="text-muted mb-0 small">@service.ShortDescription</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-info">Base: @service.BasePrice.ToString("C")</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-layer-group me-2"></i>Edit Pricing Tier: @Model.Name
                    </h6>
                </div>
                <div class="card-body">
                    <form asp-action="EditTier" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="ServiceId" type="hidden" />
                        <input asp-for="CreatedAt" type="hidden" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">Tier Name *</label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Price" class="form-label">Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input asp-for="Price" class="form-control" type="number" step="0.01" min="0" />
                                    </div>
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">Description</label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Features" class="form-label">Features</label>
                            <textarea asp-for="Features" class="form-control" rows="4"></textarea>
                            <div class="form-text">Separate features with commas. Each feature will be displayed with a checkmark.</div>
                            <span asp-validation-for="Features" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="DeliveryDays" class="form-label">Delivery Days</label>
                                    <input asp-for="DeliveryDays" class="form-control" type="number" min="1" />
                                    <span asp-validation-for="DeliveryDays" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="MinQuantity" class="form-label">Min Quantity</label>
                                    <input asp-for="MinQuantity" class="form-control" type="number" min="1" />
                                    <span asp-validation-for="MinQuantity" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="MaxQuantity" class="form-label">Max Quantity</label>
                                    <input asp-for="MaxQuantity" class="form-control" type="number" min="1" />
                                    <div class="form-text">Leave empty for unlimited</div>
                                    <span asp-validation-for="MaxQuantity" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="BadgeText" class="form-label">Badge Text</label>
                                    <input asp-for="BadgeText" class="form-control" />
                                    <span asp-validation-for="BadgeText" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="BadgeColor" class="form-label">Badge Color</label>
                                    <select asp-for="BadgeColor" class="form-select">
                                        <option value="">No Badge</option>
                                        <option value="primary">Primary (Blue)</option>
                                        <option value="success">Success (Green)</option>
                                        <option value="warning">Warning (Yellow)</option>
                                        <option value="danger">Danger (Red)</option>
                                        <option value="info">Info (Cyan)</option>
                                        <option value="secondary">Secondary (Gray)</option>
                                    </select>
                                    <span asp-validation-for="BadgeColor" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="DisplayOrder" class="form-label">Display Order</label>
                                    <input asp-for="DisplayOrder" class="form-control" type="number" min="1" />
                                    <div class="form-text">Lower numbers appear first</div>
                                    <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Options</label>
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label">Active</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="IsPopular" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsPopular" class="form-check-label">Mark as Popular</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="@Url.Action("PricingTiers", new { serviceId = service.Id })" 
                               class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Tier
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Tier Preview -->
        <div class="col-lg-4">
            <div class="card @(Model.IsPopular ? "border-primary" : "")">
                @if (Model.IsPopular && !string.IsNullOrEmpty(Model.BadgeText))
                {
                    <div class="card-header bg-primary text-white text-center py-2">
                        <small><i class="fas fa-star me-1"></i>@Model.BadgeText</small>
                    </div>
                }
                else if (!string.IsNullOrEmpty(Model.BadgeText))
                {
                    <div class="card-header <EMAIL> text-white text-center py-2">
                        <small>@Model.BadgeText</small>
                    </div>
                }

                <div class="card-body text-center">
                    <h5 class="card-title text-primary" id="previewName">@Model.Name</h5>
                    <div class="mb-3">
                        <span class="h2 text-dark" id="previewPrice">@Model.Price.ToString("C")</span>
                    </div>
                    <p class="text-muted" id="previewDescription">@Model.Description</p>
                    
                    @if (!string.IsNullOrEmpty(Model.Features))
                    {
                        <div class="text-start mb-3" id="previewFeatures">
                            <small class="text-muted">Features:</small>
                            <div class="small" id="previewFeaturesList">
                                @foreach (var feature in Model.Features.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                {
                                    <div><i class="fas fa-check text-success me-1"></i>@feature.Trim()</div>
                                }
                            </div>
                        </div>
                    }

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <small class="text-muted d-block">Delivery</small>
                                <strong id="previewDelivery">@Model.DeliveryDays days</strong>
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Order</small>
                            <strong id="previewOrder">#@Model.DisplayOrder</strong>
                        </div>
                    </div>

                    <div class="mt-3">
                        <span class="badge bg-@(Model.IsActive ? "success" : "secondary")">
                            @(Model.IsActive ? "Active" : "Inactive")
                        </span>
                        @if (Model.IsPopular)
                        {
                            <span class="badge bg-warning text-dark">Popular</span>
                        }
                    </div>
                </div>
            </div>

            <!-- Tier Info -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">Tier Information</h6>
                    <div class="small text-muted">
                        <div><strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")</div>
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div><strong>Updated:</strong> @Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</div>
                        }
                        <div><strong>Status:</strong> @(Model.IsActive ? "Active" : "Inactive")</div>
                        @if (Model.MinQuantity > 1 || Model.MaxQuantity.HasValue)
                        {
                            <div><strong>Quantity:</strong> @Model.MinQuantity@(Model.MaxQuantity.HasValue ? $" - {Model.MaxQuantity}" : "+")</div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.querySelector('[name="Name"]').value || 'Tier Name';
            const price = parseFloat(document.querySelector('[name="Price"]').value) || 0;
            const description = document.querySelector('[name="Description"]').value || 'Tier description will appear here';
            const features = document.querySelector('[name="Features"]').value;
            const deliveryDays = document.querySelector('[name="DeliveryDays"]').value || 0;
            const displayOrder = document.querySelector('[name="DisplayOrder"]').value || 1;

            document.getElementById('previewName').textContent = name;
            document.getElementById('previewPrice').textContent = '$' + price.toFixed(2);
            document.getElementById('previewDescription').textContent = description;
            document.getElementById('previewDelivery').textContent = deliveryDays + ' days';
            document.getElementById('previewOrder').textContent = '#' + displayOrder;

            // Update features
            const featuresContainer = document.getElementById('previewFeatures');
            const featuresList = document.getElementById('previewFeaturesList');
            
            if (features && features.trim()) {
                const featuresArray = features.split(',').map(f => f.trim()).filter(f => f);
                featuresList.innerHTML = featuresArray.map(feature => 
                    `<div><i class="fas fa-check text-success me-1"></i>${feature}</div>`
                ).join('');
                if (featuresContainer) featuresContainer.style.display = 'block';
            } else {
                if (featuresContainer) featuresContainer.style.display = 'none';
            }
        }

        // Add event listeners for live preview
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = ['Name', 'Price', 'Description', 'Features', 'DeliveryDays', 'DisplayOrder'];
            inputs.forEach(inputName => {
                const element = document.querySelector(`[name="${inputName}"]`);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });
        });
    </script>
}
