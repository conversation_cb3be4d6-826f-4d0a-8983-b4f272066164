using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories;

public class QuoteRepository : Repository<Quote>, IQuoteRepository
{
    public QuoteRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<Quote?> GetByQuoteNumberAsync(string quoteNumber)
    {
        return await _context.Quotes
            .FirstOrDefaultAsync(q => q.QuoteNumber == quoteNumber && !q.IsDeleted);
    }

    public async Task<Quote?> GetWithItemsAsync(int id)
    {
        return await _context.Quotes
            .Include(q => q.Items)
                .ThenInclude(qi => qi.Service)
            .Include(q => q.Items)
                .ThenInclude(qi => qi.ServicePricingTier)
            .Include(q => q.Client)
            .FirstOrDefaultAsync(q => q.Id == id && !q.IsDeleted);
    }

    public async Task<Quote?> GetWithItemsAndFeaturesAsync(int id)
    {
        return await _context.Quotes
            .Include(q => q.Items)
                .ThenInclude(qi => qi.Service)
            .Include(q => q.Items)
                .ThenInclude(qi => qi.ServicePricingTier)
            .Include(q => q.Items)
                .ThenInclude(qi => qi.SelectedFeatures)
                    .ThenInclude(qif => qif.ServiceFeature)
            .Include(q => q.Client)
            .FirstOrDefaultAsync(q => q.Id == id && !q.IsDeleted);
    }

    public async Task<IEnumerable<Quote>> GetByClientEmailAsync(string clientEmail)
    {
        return await _context.Quotes
            .Where(q => q.ClientEmail == clientEmail && !q.IsDeleted)
            .OrderByDescending(q => q.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<Quote>> GetByStatusAsync(string status)
    {
        return await _context.Quotes
            .Where(q => q.Status == status && !q.IsDeleted)
            .OrderByDescending(q => q.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<Quote>> GetExpiredQuotesAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Quotes
            .Where(q => q.ExpiresAt.HasValue && q.ExpiresAt < now && 
                       q.Status == "Sent" && !q.IsDeleted)
            .ToListAsync();
    }

    public async Task<string> GenerateQuoteNumberAsync()
    {
        var year = DateTime.UtcNow.Year;
        var prefix = $"QT{year}";
        
        var lastQuote = await _context.Quotes
            .Where(q => q.QuoteNumber.StartsWith(prefix))
            .OrderByDescending(q => q.QuoteNumber)
            .FirstOrDefaultAsync();

        if (lastQuote == null)
        {
            return $"{prefix}0001";
        }

        var lastNumber = lastQuote.QuoteNumber.Substring(prefix.Length);
        if (int.TryParse(lastNumber, out var number))
        {
            return $"{prefix}{(number + 1):D4}";
        }

        return $"{prefix}0001";
    }

    public async Task<decimal> CalculateQuoteTotalAsync(int quoteId)
    {
        var quote = await GetWithItemsAndFeaturesAsync(quoteId);
        if (quote == null) return 0;

        var subtotal = quote.Items.Sum(item => 
            item.TotalPrice + item.SelectedFeatures.Sum(f => f.TotalPrice));

        var discountAmount = subtotal * (quote.DiscountPercentage / 100);
        var taxableAmount = subtotal - discountAmount;
        var taxAmount = taxableAmount * (quote.TaxRate / 100);

        return taxableAmount + taxAmount;
    }

    public async Task<bool> UpdateQuoteStatusAsync(int quoteId, string status)
    {
        var quote = await GetByIdAsync(quoteId);
        if (quote == null) return false;

        quote.Status = status;
        
        if (status == "Sent" && !quote.SentAt.HasValue)
        {
            quote.SentAt = DateTime.UtcNow;
            quote.ExpiresAt = DateTime.UtcNow.AddDays(30); // Default 30 days expiry
        }
        else if (status == "Accepted" && !quote.AcceptedAt.HasValue)
        {
            quote.AcceptedAt = DateTime.UtcNow;
        }

        await UpdateAsync(quote);
        return true;
    }

    public async Task<IEnumerable<Quote>> GetQuotesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.Quotes
            .Where(q => q.CreatedAt >= startDate && q.CreatedAt <= endDate && !q.IsDeleted)
            .OrderByDescending(q => q.CreatedAt)
            .ToListAsync();
    }

    public async Task<Dictionary<string, int>> GetQuoteStatusCountsAsync()
    {
        return await _context.Quotes
            .Where(q => !q.IsDeleted)
            .GroupBy(q => q.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count());
    }

    public async Task<decimal> GetTotalQuoteValueByStatusAsync(string status)
    {
        return await _context.Quotes
            .Where(q => q.Status == status && !q.IsDeleted)
            .SumAsync(q => q.TotalAmount);
    }
}
